class PDFDownloadResponseDTO:
    """
    Data transfer object representing the response details for PDF downloads.
    """

    def __init__(self, **kwargs):
        self._raw_link = kwargs.get("raw_link")
        self._bucket = kwargs.get("bucket")
        self._object_link = kwargs.get("object_link")
        self._message_trace_id = kwargs.get("message_trace_id")
        self._theme_ids = kwargs.get("theme_ids")
        self._title = kwargs.get("title")
        self._org_id = kwargs.get("org_id")
        self._user_id = kwargs.get("user_id")
        self._document_type = kwargs.get("document_type")

    def get_raw_link(self):
        return self._raw_link

    def get_bucket(self):
        if not self._bucket:
            return ""
        return self._bucket

    def get_object_link(self):
        if not self._bucket:
            return ""
        return self._object_link

    def get_message_trace_id(self):
        return self._message_trace_id

    def get_title(self):
        return self._title

    def get_theme_ids(self):
        return self._theme_ids

    def get_org_id(self):
        return self._org_id

    def get_user_id(self):
        return self._user_id

    def get_document_type(self):
        return self._document_type
