name: Linting, Testing and Code Coverage

on:
  pull_request_target:
    branches:
      - development
      - main
      - qa

jobs:
  branch_naming_check:
    name: Enforce Branch Naming Convention
    if: github.event.pull_request.base.ref == 'development'
    runs-on: ubuntu-latest

    steps:
      - name: Check branch name
        run: |
          branch_name="${{ github.event.pull_request.head.ref }}"
          echo "Checking branch name: $branch_name"
          if [[ ! "$branch_name" =~ ^(bugfix|feature|hotfix)\/(PROD|prod)-[0-9]+-[a-zA-Z0-9\-]+$ ]]; then
            echo "Invalid branch name: $branch_name"
            echo "Branch name must follow the convention: feature/prod-XXXX-text "
            exit 1
          fi

  linting_and_formatting:
      name: Linting and Formatting
      runs-on: ubuntu-latest
      strategy:
        matrix:
          python-version: [ "3.12" ]

      steps:
        - name: Checkout code  to ${{ github.event.pull_request.head.sha }} commit
          uses: actions/checkout@v3
          with:
            ref: ${{ github.event.pull_request.head.sha }}
        - name: Set up Python  ${{ matrix.python-version }}
          uses: actions/setup-python@v3
          with:
            python-version: ${{ matrix.python-version }}
        - name: Install dependencies
          run: |
            pip install -r dev-requirements.txt
            export PYTHONPATH=.
        - name: Run pre-commit hooks
          env:
            AWS_DEFAULT_REGION: ap-south-1
          run: pre-commit run --config .pre-commit-config-format.yaml --all-files

  pylint_and_unittest:
    name: Pylint and Unit Test Check
    runs-on: ubuntu-latest
    services:
      mongodb:
        image: mongo
        ports:
          - 27017:27017
    strategy:
      matrix:
        python-version: [ "3.12" ]

    steps:
      - name: Checkout code  to ${{ github.event.pull_request.head.sha }} commit
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Set up Python  ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          pip install -r dev-requirements.txt
          export PYTHONPATH=.
      - name: Run pre-commit hooks
        env:
          AWS_DEFAULT_REGION: ap-south-1
        run: pre-commit run --config .pre-commit-config-pylint.yaml --all-files
