Install python related dependencies
```commandline
pip install -r dev-requirements.txt
```

Install the pre-commit hooks

```commandline
pre-commit install
```

Run Commit Hooks on all files manually
```commandline
pre-commit run --all-files
```

Run particular hook manually
pre-commit run hook_id
```commandline
pre-commit run run-unit-tests
```


On commit, this pre-commit hook will be running and if this is passed then you are allowed to commit
