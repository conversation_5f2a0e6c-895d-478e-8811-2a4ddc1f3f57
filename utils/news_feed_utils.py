from datetime import datetime

from dateutil.relativedelta import relativedelta

from config import DATE_FORMAT
from config import GO<PERSON><PERSON><PERSON>_BASE_URL
from config import GOOGLE_NEWS_AUX_PARAMS
from config import GOOGLE_NEWS_END_DATE
from config import GO<PERSON><PERSON><PERSON>_NEWS_URL
from config import NEWS_PARSER_SERVICE_CONFIG
from constants.api_parameters import APIParameters
from constants.entities import DataAuditLogs
from constants.entities import FeedSources
from constants.entities import LambdaNameEntities
from constants.entities import LambdaRunStatus
from constants.entities import PrimarySourceEntities
from service.audit_service import AuditService
from utils.date_utils import datetime_to_str
from utils.entities.collection_entities import FeedParserJobStatusEntities
from utils.exceptions.exceptions import CustomException
from utils.general_utils import DEFAULT_NEWS_DATE
from utils.general_utils import get_incorporation_date
from utils.logger import get_logger

logger = get_logger(__name__)


def news_parser_existing_company_date_handling(last_triggered_time):
    logger.info(
        "Setting start and end date for existing company towards forward approach"
    )
    parser_job_doc = {}
    current_datetime_now = datetime.now()
    news_till_date = last_triggered_time.get(
        FeedParserJobStatusEntities.NEWS_TILL_DATE.value, datetime.now()
    )
    parser_job_doc[FeedParserJobStatusEntities.NEWS_FROM_DATE.value] = (
        news_till_date
    )
    parser_job_doc[FeedParserJobStatusEntities.NEWS_TILL_DATE.value] = (
        current_datetime_now
    )
    return (
        parser_job_doc[FeedParserJobStatusEntities.NEWS_FROM_DATE.value],
        parser_job_doc[FeedParserJobStatusEntities.NEWS_TILL_DATE.value],
        parser_job_doc,
    )


def google_parser_existing_company_date_handling(
    last_triggered_time, feed_source
):
    logger.info("setting start and end date for existing company")
    parser_job_status_doc = {}
    date_of_inc = last_triggered_time[
        FeedParserJobStatusEntities.DATE_OF_INC.value
    ]
    news_from_date = last_triggered_time[
        FeedParserJobStatusEntities.NEWS_FROM_DATE.value
    ]
    logger.info(f"company date of inc:{date_of_inc}")
    if news_from_date >= (date_of_inc - relativedelta(months=3)):
        logger.info(
            "setting dates towards backward approach reaching to date of inc."
        )
        start_date = news_from_date
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, "%d-%m-%Y")

        end_date = start_date - relativedelta(
            months=NEWS_PARSER_SERVICE_CONFIG.get(feed_source).get(
                FeedParserJobStatusEntities.DURATION_MONTHS.value
            )
        )
        parser_job_status_doc[
            FeedParserJobStatusEntities.NEWS_FROM_DATE.value
        ] = end_date
    else:
        logger.info(
            "setting dates towards forward approach reaching towards current date."
        )
        end_date = last_triggered_time[
            FeedParserJobStatusEntities.NEWS_TILL_DATE.value
        ]

        if (
            FeedParserJobStatusEntities.NEWS_START_DATE.value
            in last_triggered_time
        ):
            news_start_date = last_triggered_time[
                FeedParserJobStatusEntities.NEWS_START_DATE.value
            ]
        else:
            news_start_date = datetime.now()
            parser_job_status_doc[
                FeedParserJobStatusEntities.NEWS_START_DATE.value
            ] = news_start_date
        if isinstance(news_start_date, str):
            news_start_date = datetime.strptime(news_start_date, "%d-%m-%Y")

        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, "%d-%m-%Y")

        if feed_source == FeedSources.GOOGLE_NEWS.value and end_date >= (
            news_start_date + relativedelta(months=1)
        ):
            logger.info("We have extracted all the past news.")
            return None, None, None

        months_later = end_date + relativedelta(
            months=NEWS_PARSER_SERVICE_CONFIG.get(feed_source).get(
                FeedParserJobStatusEntities.DURATION_MONTHS.value
            )
        )
        start_date = min(months_later, datetime.now())

        parser_job_status_doc[
            FeedParserJobStatusEntities.NEWS_TILL_DATE.value
        ] = start_date

    return (
        min(start_date, end_date),
        max(start_date, end_date),
        parser_job_status_doc,
    )


def is_feed_available(feeds):
    feed_available = True
    parser_flagged = False
    if feeds["bozo"] == 1:
        logger.error(
            f"Google Parser has been flagged {feeds['feed']['summary']}"
        )
        parser_flagged = True
    if len(feeds["entries"]) == 0:
        feed_available = False
        logger.info("Feed not available for the given duration")
    return feed_available, parser_flagged


def get_feed_url(company, start_date_str, end_date_str):
    if isinstance(company, list):
        company = ["%20".join(name.split()) for name in company]
        company = "%20or%20".join(company)
    else:
        words = company.split()
        if len(words) > 1:
            company = company.replace(" ", "%20")

    formatted_start_date = start_date_str.strftime(DATE_FORMAT)
    if end_date_str:
        formatted_end_date = end_date_str.strftime(DATE_FORMAT)
        feed_url = (
            GOOGLE_NEWS_URL.format(
                google_base_url=GOOGLE_BASE_URL,
                company=company,
                start_date_str=formatted_start_date,
            )
            + GOOGLE_NEWS_END_DATE.format(end_date_str=formatted_end_date)
            + GOOGLE_NEWS_AUX_PARAMS
        )
    else:
        feed_url = (
            GOOGLE_NEWS_URL.format(
                google_base_url=GOOGLE_BASE_URL,
                company=company,
                start_date_str=formatted_start_date,
            )
            + GOOGLE_NEWS_AUX_PARAMS
        )
    logger.info(
        f"Got the feed url={feed_url} for the company={company}, "
        f"start_date_str={start_date_str}, end_date_str={end_date_str}"
    )
    return feed_url


def fetch_company_date_of_incorporation(company_cin, company_record):
    logger.info(f"fetching date of incorporation for cin={company_cin}")
    try:
        return get_incorporation_date(company_record)
    except CustomException as exc:
        logger.exception(
            f"Invalid incorporation dateformat provided for the company_cin={company_cin},"
            f" company_name={company_record.get(APIParameters.COMPANY_NAME_FIELD.value)}. "
            f"Please format to get the news   and  exc={exc}"
        )
        return DEFAULT_NEWS_DATE


def send_to_audit_logs(**kwargs):
    service_id = kwargs.get(DataAuditLogs.SERVICE_ID.value)
    company_cin = kwargs.get(DataAuditLogs.COMPANY_CIN.value)
    start_date = kwargs.get(DataAuditLogs.START_DATE.value)
    end_date = kwargs.get(DataAuditLogs.END_DATE.value)
    parser_flagged = kwargs.get(DataAuditLogs.PARSER_FLAGGED.value)
    feed_available = kwargs.get(DataAuditLogs.FEED_AVAILABLE.value)
    messages_published_count = kwargs.get(
        DataAuditLogs.MESSAGES_PUBLISHED.value
    )
    if isinstance(start_date, datetime):
        start_date = datetime_to_str(start_date, "%Y-%m-%d %H:%M:%S")
    if isinstance(end_date, datetime):
        end_date = datetime_to_str(end_date, "%Y-%m-%d %H:%M:%S")
    publishing_urls = kwargs.get(DataAuditLogs.PUBLISHING_URLS.value)
    audit_logs = {
        DataAuditLogs.PRIMARY_SOURCE.value: PrimarySourceEntities.NEWS.value,
        DataAuditLogs.COMPANY_CIN.value: company_cin,
        DataAuditLogs.START_DATE.value: start_date,
        DataAuditLogs.END_DATE.value: end_date,
        DataAuditLogs.PARSER_FLAGGED.value: parser_flagged,
        DataAuditLogs.FEED_AVAILABLE.value: feed_available,
        DataAuditLogs.MESSAGES_PUBLISHED.value: messages_published_count,
        DataAuditLogs.FEEDS_INSERTED.value: len(publishing_urls),
        DataAuditLogs.LAMBDA.value: LambdaNameEntities.GET_NEWS_FEED.value,
    }
    if not parser_flagged:
        audit_logs[DataAuditLogs.RESULT.value] = LambdaRunStatus.SUCCESS.value
    else:
        audit_logs[DataAuditLogs.RESULT.value] = LambdaRunStatus.FAILED.value
        audit_logs[DataAuditLogs.MESSAGE.value] = (
            "Diffbot Parser has been flagged"
        )
    audit_service = AuditService(service_id)
    audit_service.send_to_audit_queue(body=audit_logs)
