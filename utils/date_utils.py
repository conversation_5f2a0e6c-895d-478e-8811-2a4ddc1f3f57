import datetime


def current_datetime():
    return datetime.datetime.now()


def str_to_datetime(timestamp, date_format="%Y-%m-%d %H:%M:%S"):
    if not timestamp or timestamp == "None":
        return None
    return datetime.datetime.strptime(timestamp, date_format)


def datetime_to_str(timestamp, date_format):
    if not timestamp or timestamp == "None":
        return ""
    return timestamp.strftime(date_format)


def timestamp_to_datetime(timestamp):
    return datetime.datetime.fromtimestamp(timestamp / 1000)
