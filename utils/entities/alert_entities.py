from enum import Enum


class AlertNameEntities(Enum):
    """
    alert name entities
    """

    ALERTS: str = "google_alerts"


class AlertSourceEntities(Enum):
    """
    alert source entities
    """

    GMAIL: str = "gmail"


class AlertInputEntities(Enum):
    """
    newsletter input event entities
    """

    ALERT_SOURCE = "alert_source"
    ALERT_EMAIL = "alert_email"
    ALERT_PASSWORD = "alert_password"
    ALERT_NAME = "alert_name"
    ALERT_SEARCH_CRITERIA = "alert_search_criteria"
    ALERT_DATE_FORMAT = "alert_date_format"


class AlertEmailResponseEntities(Enum):
    """
    alert email response entities
    """

    ALERT_NAME: str = "alert_name"
    FROM_ADDRESS: str = "from_address"
    TO_ADDRESS: str = "to_address"
    DATE: str = "date"
    SUBJECT: str = "subject"
    BODY: str = "body"
    CONTENT_TYPE: str = "content_type"
    EMAIL_ID: str = "email_id"


class AlertEntities(Enum):
    """
    ALl the entities relating to alerts.
    """

    TITLE: str = "title"
    DESCRIPTION: str = "description"
    CIN: str = "cin"
    COMPANY_NAME: str = "company_name"
    NEWS_LINK: str = "news_link"
    NEWS_SOURCE: str = "news_source"
    NEWS_IMAGE: str = "news_image"
    PUBLISHED_DATE: str = "published_date"
