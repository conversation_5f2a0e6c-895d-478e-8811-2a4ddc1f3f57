from enum import Enum

from utils.entities.newsletter_entities import NewsLetterEmailResponseEntities


class SearchCriteriaEntities(Enum):
    """
    Entities relating to search criteria.
    """

    FROM_ADDRESS: str = "from_address"
    TO_ADDRESS: str = "to_address"
    FROM_DATE: str = "from_date"
    BEFORE_DATE: str = "before_date"
    SUBJECT_TEXT: str = "subject_text"
    BODY_TEXT: str = "body_text"
    EMAIL_NUMBER: str = "email_number"


class SearchResponseEntities(Enum):
    """
    Entities relating to search response.
    """

    EMAIL_ID: str = NewsLetterEmailResponseEntities.EMAIL_ID.value
    FROM_ADDRESS: str = NewsLetterEmailResponseEntities.FROM_ADDRESS.value
    TO_ADDRESS: str = NewsLetterEmailResponseEntities.TO_ADDRESS.value
    DATE: str = NewsLetterEmailResponseEntities.DATE.value
    SUBJECT: str = NewsLetterEmailResponseEntities.SUBJECT.value
    BODY: str = NewsLetterEmailResponseEntities.BODY.value
    CONTENT_TYPE = NewsLetterEmailResponseEntities.CONTENT_TYPE.value
