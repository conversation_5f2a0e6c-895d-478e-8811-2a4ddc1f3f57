from enum import Enum


class CompanyInfoEntities(Enum):
    """
    Entities relating to the Company Info Collection.
    """

    COMPANY: str = "company"
    ALIAS_NAME: str = "alias_name"
    CIN: str = "cin"
    NAME: str = "name"
    NAME_FOR_NEWS: str = "name_for_news"
    IS_ACTIVE: str = "is_active"
    IS_READY: str = "is_ready"
    SOCIAL_NETWORK_LINKS: str = "social_network_links"
    COMPANY_MASTER_SUMMARY: str = "company_master_summary"


class FeedUrlEntities(Enum):
    """Feed URL entity attributes."""

    ID: str = "_id"
    NAME: str = "name"
    CIN: str = "cin"
    RAW_LINK: str = "raw_link"
    NEWS_LINK: str = "news_link"
    IMAGE_LINK: str = "image_link"
    SOURCE: str = "source"
    PARSER_SERVICE: str = "parser_service"
    RAW_TITLE: str = "raw_title"
    RAW_DESCRIPTION: str = "raw_description"
    PUBLISHED_DATE: str = "published_date"
    PROCESSED_DATE: str = "processed_date"
    IGNORE_PUBLISHING: str = "ignore_publishing"
    DENOISING_STATUS: str = "denoising_status"
    NAME_PRESENT_IN_TITLE: str = "name_present_in_title"
    NAME_COUNT_IN_TEXT: str = "name_count_in_text"
    IS_NEWS: str = "is_news"
    IS_DENOISED: str = "is_denoised"
    DENOISED_LINK: str = "denoised_link"
    ARTICLE_THEME: str = "article_theme"
    KEY_POINTS: str = "key_points"
    SERVICE_ID: str = "service_id"
    CATEGORIES: str = "categories"
    COUNTRIES: str = "countries"
    ARTICLE_IMAGE: str = "article_image"
    TAGS: str = "tags"
    ENTITY_TYPE: str = "entity_type"
    ENTITY_ID: str = "entity_id"
    S3_BUCKET_NAME: str = "s3_bucket_name"
    S3_OBJECT_KEY: str = "s3_object_key"


class FeedParserJobStatusEntities(Enum):
    """
    Entities relating to the Feed Parser Job Status Collection.
    """

    CIN: str = "cin"
    PARSER_SERVICE = "parser_service"
    LAST_RUN_END_DATE = "last_run_end_date"
    LAST_RUN_START_DATE = "last_run_start_date"
    LAST_RUN_DETAILS = "last_run_details"
    EMAIL_NUMBER: str = "email_number"
    EMAIL_DATE: str = "email_date"
    ID: str = "_id"
    NEWS_TILL_DATE: str = "news_till_date"
    NEWS_FROM_DATE: str = "news_from_date"
    NEWS_DELTA: str = "news_delta"
    DATE_OF_INC: str = "date_of_inc"
    NEWS_START_DATE: str = "news_start_date"
    BATCH_SIZE: str = "BATCH_SIZE"
    DURATION_MONTHS: str = "DURATION_MONTHS"
