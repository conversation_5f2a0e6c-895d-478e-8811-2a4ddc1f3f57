from enum import Enum


class NewsletterNameEntities(Enum):
    """Enum for different newsletter names."""

    INC42: str = "inc42 media"
    ARC: str = "arc"
    ALERTS: str = "google_alerts"


class NewsletterSourceEntities(Enum):
    """Enum for newsletter sources."""

    GMAIL: str = "gmail"


class NewsletterInputEntities(Enum):
    """
    newsletter input event entities
    """

    NEWSLETTER_SOURCE = "newsletter_source"
    NEWSLETTER_EMAIL = "newsletter_email"
    NEWSLETTER_PASSWORD = "newsletter_password"
    NEWSLETTER_NAME = "newsletter_name"
    NEWSLETTER_SEARCH_CRITERIA = "newsletter_search_criteria"
    NEWSLETTER_DATE_FORMAT = "newsletter_date_format"


class NewsLetterEmailResponseEntities(Enum):
    """Enum for email response entities in newsletters."""

    NEWSLETTER_NAME: str = "newsletter_name"
    FROM_ADDRESS: str = "from_address"
    TO_ADDRESS: str = "to_address"
    DATE: str = "date"
    SUBJECT: str = "subject"
    BODY: str = "body"
    CONTENT_TYPE: str = "content_type"
    EMAIL_ID: str = "email_id"


class NewsLetterEntities(Enum):
    """Enum for various newsletter-related entities."""

    TITLE: str = "title"
    DESCRIPTION: str = "description"
    CIN: str = "cin"
    COMPANY_NAME: str = "company_name"
    NEWS_LINK: str = "news_link"
    NEWS_SOURCE: str = "news_source"
    NEWS_IMAGE: str = "news_image"
    PUBLISHED_DATE: str = "published_date"
    SECTOR: str = "sector"
    COMPANY: str = "company"
