from enum import Enum


class PDFDownloaderEntities(Enum):
    """
    Entities relating to the PDF Downloader Payload Response.
    """

    WEBLINK: str = "weblink"
    OBJECT_KEY: str = "object_key"
    BUCKET_NAME: str = "bucket_name"
    TITLE: str = "title"
    MSG_TRC_ID: str = "message_trace_id"
    THEME_IDS: str = "theme_ids"
    ORG_ID: str = "org_id"
    USER_ID: str = "user_id"
    DOC_TYPE: str = "document_type"


class ServiceEntities(Enum):
    """
    All service related variable can be stored here, should be consistent across the pipeline
    """

    SECTOR_NEWS = "sector-news"
    RSS_FEED_PARSER = "rss-feed-parser"
    COMPANY_CIN = "company_cin"
    RAW_LINK = "raw_link"
    NEWS_LINK = "news_link"
    NEWS_TITLE = "news_title"
    ENTITY_TYPE = "entity_type"
    ENTITY_ID = "entity_id"


class EntityTypes(Enum):
    """
    Entity Types
    """

    COMPANY = "company"
    SECTOR = "sector"


class S3Entities(Enum):
    """
    All S3 related variable can be stored here, should be consistent across the pipeline
    """

    BODY: str = "Body"
    NEWS_FOLDER: str = "news"
