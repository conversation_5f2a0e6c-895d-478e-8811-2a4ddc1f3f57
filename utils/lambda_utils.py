import json

import boto3

from utils.logger import get_logger

logger = get_logger(__name__)

lambda_client = boto3.client("lambda")


def invoke_lambda(function_arn, invocation_type, payload):
    logger.info(
        f"Calling the lambda function name ={function_arn} with invocation type ={invocation_type}"
    )

    response = lambda_client.invoke(
        FunctionName=function_arn,
        InvocationType=invocation_type,
        Payload=payload,
    )

    payload = json.load(response.get("Payload"))
    logger.info(
        f"Successfully invoked the lambda and got the response code  = {payload.get('statusCode')}"
    )

    return payload
