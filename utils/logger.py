import logging
import os
import sys
import time

FORMATTER = logging.Formatter(
    "%(asctime)s %(levelname)s [%(filename)s:%(lineno)d]  [%(name)s] %(message)s"
)


def get_level(log_level):
    level = logging.INFO
    if log_level == "DEBUG":
        level = logging.DEBUG

    return level


def get_console_handler():
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(FORMATTER)
    return console_handler


def get_logger(logger_name):
    logger = logging.getLogger(logger_name)
    log_level = os.environ.get("LOG_LEVEL", "INFO")
    logger.setLevel(get_level(log_level))
    logger.addHandler(get_console_handler())
    logger.propagate = False
    return logger


def timer(fun):
    def wrapper(*args, **kwargs):
        start = time.time()
        result = fun(*args, **kwargs)
        end = time.time()
        logger = get_logger(__name__)
        logger.info(f"Time taken by {fun.__name__!r} is {(end - start):.4f}s")
        return result

    return wrapper
