import boto3

from utils.logger import get_logger

logger = get_logger(__name__)


def invoke_lambda_async(function_arn, invocation_type, payload):
    lambda_client = boto3.client("lambda")
    logger.info(
        "Calling the lambda function name = %s with invocation type = %s",
        function_arn,
        invocation_type,
    )
    response = lambda_client.invoke(
        FunctionName=function_arn,
        InvocationType=invocation_type,
        Payload=payload,
    )
    response = response["Payload"].read().decode("utf-8")
    logger.info("Successfully invoked the lambda with response: %s", response)

    return response
