import boto3

from constants.entities import SQSEntities
from utils.logger import get_logger

sqs_client = boto3.client("sqs")

logger = get_logger(__name__)


class SqsService:
    """
    Service for sending messages to an AWS SQS queue with logging and error handling.
    """

    @staticmethod
    def send_message_to_queue(
        queue_url: str, message: str, message_group_id: str
    ):
        payload = {}
        if not queue_url:
            logger.error("Queue URL provided is empty")
            return

        payload[SQSEntities.QUEUE_URL.value] = queue_url
        payload[SQSEntities.MESSAGE_BODY.value] = message
        if message_group_id:
            payload[SQSEntities.MESSAGE_GROUP_ID.value] = message_group_id

        logger.debug(
            f"Started publishing the message to the queue with "
            f"details={payload} with message_group_id={message_group_id}"
        )
        try:
            response = sqs_client.send_message(**payload)
            logger.debug(response)
        except Exception as exc:
            logger.error(f"Unable to send message because of {exc}")
            raise exc

        logger.debug("Finished publishing the message to the queue")
