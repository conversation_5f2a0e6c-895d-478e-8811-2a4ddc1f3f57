from datetime import date

from utils.entities.collection_entities import FeedUrlEntities
from utils.logger import get_logger

logger = get_logger(__name__)


def get_news_report_queries(
    user_date: date,
    is_denoised_flag: bool,
    is_news: bool,
    group_by: str = None,
):
    logger.info(
        f"Getting news report queries on date = {user_date} with group by ={group_by}"
    )

    match_query_fields = {
        FeedUrlEntities.CIN.value: {"$exists": True, "$ne": None},
        FeedUrlEntities.PARSER_SERVICE.value: {"$exists": True, "$ne": None},
        FeedUrlEntities.IS_DENOISED.value: {"$eq": is_denoised_flag},
        FeedUrlEntities.IS_NEWS.value: {"$eq": is_news},
        FeedUrlEntities.PROCESSED_DATE.value: {"$gte": user_date},
    }
    if group_by == FeedUrlEntities.CIN.value:
        group_by_fields = {
            FeedUrlEntities.PROCESSED_DATE.value: {
                "$substr": [f"${FeedUrlEntities.PROCESSED_DATE.value}", 0, 10]
            },
            FeedUrlEntities.CIN.value: f"${FeedUrlEntities.CIN.value}",
        }
    elif group_by == FeedUrlEntities.PARSER_SERVICE.value:
        group_by_fields = {
            FeedUrlEntities.PROCESSED_DATE.value: {
                "$substr": [f"${FeedUrlEntities.PROCESSED_DATE.value}", 0, 10]
            },
            FeedUrlEntities.PARSER_SERVICE.value: f"${FeedUrlEntities.PARSER_SERVICE.value}",
        }
    else:
        group_by_fields = {
            FeedUrlEntities.PROCESSED_DATE.value: {
                "$substr": [f"${FeedUrlEntities.PROCESSED_DATE.value}", 0, 10]
            },
            FeedUrlEntities.PARSER_SERVICE.value: f"${FeedUrlEntities.PARSER_SERVICE.value}",
            FeedUrlEntities.CIN.value: f"${FeedUrlEntities.CIN.value}",
        }

    if not user_date:
        group_by_fields.pop(FeedUrlEntities.PROCESSED_DATE.value)
        match_query_fields.pop(FeedUrlEntities.PROCESSED_DATE.value)

    return [
        {"$match": match_query_fields},
        {"$group": {"_id": group_by_fields, "count": {"$sum": 1}}},
    ]
