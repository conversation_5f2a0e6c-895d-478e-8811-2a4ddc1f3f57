from DTOs.pdf_downloader_response_dto import PDFDownloadResponseDTO
from utils.entities.event_entities import PDFDownloaderEntities


def get_pdf_download_request_payload(
    pdf_downloader_response_data: PDFDownloadResponseDTO,
):
    return {
        PDFDownloaderEntities.WEBLINK.value: pdf_downloader_response_data.get_raw_link(),
        PDFDownloaderEntities.OBJECT_KEY.value: pdf_downloader_response_data.get_object_link(),
        PDFDownloaderEntities.BUCKET_NAME.value: pdf_downloader_response_data.get_bucket(),
        PDFDownloaderEntities.TITLE.value: pdf_downloader_response_data.get_title(),
        PDFDownloaderEntities.MSG_TRC_ID.value: pdf_downloader_response_data.get_message_trace_id(),
        PDFDownloaderEntities.THEME_IDS.value: pdf_downloader_response_data.get_theme_ids(),
        PDFDownloaderEntities.ORG_ID.value: pdf_downloader_response_data.get_org_id(),
        PDFDownloaderEntities.USER_ID.value: pdf_downloader_response_data.get_user_id(),
        PDFDownloaderEntities.DOC_TYPE.value: pdf_downloader_response_data.get_document_type(),
    }
