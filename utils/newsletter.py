from utils.entities.newsletter_entities import NewsLetterEntities


def get_newsletter_details(
    title: str,
    description: str,
    news_link: str,
    news_source: str,
    news_image: str = None,
) -> dict:

    return {
        NewsLetterEntities.TITLE.value: title.strip(),
        NewsLetterEntities.DESCRIPTION.value: description.strip(),
        NewsLetterEntities.NEWS_LINK.value: news_link,
        NewsLetterEntities.NEWS_SOURCE.value: news_source,
        NewsLetterEntities.NEWS_IMAGE.value: news_image,
    }
