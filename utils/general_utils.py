import json
import re
from datetime import date
from datetime import datetime
from urllib.parse import urlparse

import boto3
import requests
from bs4 import BeautifulSoup
from urllib3.exceptions import LocationParseError

import config
from config import AZURE_NER_ENDPOINT
from config import AZURE_NER_KEY
from config import DENOISING_SQS_URL
from config import INCORPORATION_DATE_FORMAT
from config import INSERT_FEED_SERVICE_SQS_URL
from config import INSERT_NEWS_FEED_QUEUE_BATCH_SIZE
from constants.api_parameters import APIParameters
from constants.entities import OGConstants
from service.ObjectFactoryStore.nosql_store import NoSQLStore
from service.ObjectFactoryStore.s3_store import S3Store
from utils.date_utils import str_to_datetime
from utils.exceptions.exceptions import CustomException
from utils.logger import get_logger
from utils.og_utils import KNOWN_OPENGRAPH_TAGS
from utils.sqs import SqsService

logger = get_logger(__name__)
DEFAULT_NEWS_DATE = datetime(2018, 1, 1)
NEWS_FEED_SOURCES = config.NEWS_FEED_SOURCES


def get_domain(url):
    logger.info(f"Getting domain for the url={url}")
    parsed_url = urlparse(url)
    domain = parsed_url.netloc

    # Remove username and password if present
    domain = re.sub(r"^[^@]*@", "", domain)

    # Remove port number if present
    domain = re.sub(r":\d+$", "", domain)

    # Remove leading "www." if present
    domain = re.sub(r"^www\.", "", domain)

    return domain


def get_incorporation_date(company_info):
    incorporation_date = None
    summary = company_info.get(
        APIParameters.COMPANY_MASTER_SUMMARY.value, None
    )
    if summary:
        incorporation_date = summary.get(
            APIParameters.INCORPORATION_DATE_FIELD.value, None
        )

    if (
        not incorporation_date
        or incorporation_date == "None"
        or incorporation_date == "none"
    ):
        incorporation_date = DEFAULT_NEWS_DATE

    if not isinstance(incorporation_date, datetime):
        incorporation_date = str_to_datetime(
            timestamp=incorporation_date, date_format=INCORPORATION_DATE_FORMAT
        )
    logger.info(f"Date of incorporation of company: {incorporation_date}")
    return incorporation_date


def alter_date_format(input_date_str, current_format, expected_format):
    input_date = datetime.strptime(input_date_str, current_format)
    output_date_str = input_date.strftime(expected_format)
    return output_date_str


def get_connection(service):
    # Create a Boto3 client for S3 with the access keys
    service_connection = boto3.client(service)

    return service_connection


def get_object_store(db_credentials):
    driver = db_credentials["DRIVER"]
    storage_object = None
    # TODO: Remove this as it is not being used anymore.
    # if driver == "mysql+pymysql":
    #     storage_object = SQLStore(db_credentials)
    if driver == "s3":
        storage_object = S3Store(db_credentials)
    elif driver == "mongodb":
        storage_object = NoSQLStore(db_credentials)
    else:
        logger.error(f"Invalid driver input={driver}")
    return storage_object


def batchify(lst, batch_size):
    return [lst[i : i + batch_size] for i in range(0, len(lst), batch_size)]


def publish_messages(urls: list, message_group_id=None):
    logger.info(f"Total no of urls to be published = {len(urls)}")

    for index, url_detail in enumerate(urls):
        SqsService.send_message_to_queue(
            queue_url=DENOISING_SQS_URL,
            message=json.dumps(url_detail),
            message_group_id=(
                message_group_id
                if message_group_id
                else f"BATCH_{str(index % INSERT_NEWS_FEED_QUEUE_BATCH_SIZE)}"
            ),
        )

    logger.info("Published all the messages")

    return len(urls)


def send_to_insert_feed_service_queue(urls: list, message_group_id=None):
    logger.info(
        f"Total no of urls to be published in insert feed service queue = {len(urls)}"
    )
    for index, url_detail in enumerate(urls):
        SqsService.send_message_to_queue(
            queue_url=INSERT_FEED_SERVICE_SQS_URL,
            message=json.dumps(url_detail),
            message_group_id=(
                message_group_id
                if message_group_id
                else f"BATCH_{str(index % INSERT_NEWS_FEED_QUEUE_BATCH_SIZE)}"
            ),
        )

    logger.info("Published all the messages")
    return len(urls)


def get_redirect_url(original_url: str, recursive_count: int = 0):
    logger.debug(
        f"Getting the redirect url for the original url={original_url}"
    )
    try:
        original_url_object = urlparse(original_url)
        response = requests.get(
            original_url, allow_redirects=False, timeout=30
        )
        if response.status_code in (302, 301):
            redirect_url = response.headers["Location"]
            redirect_url_object = urlparse(redirect_url)

            if (
                not redirect_url_object.scheme
                or not redirect_url_object.netloc
            ):
                redirect_url = (
                    f"{original_url_object.scheme}:"
                    f"//{original_url_object.netloc}{redirect_url}"
                )

            if recursive_count < 4:
                return get_redirect_url(
                    original_url=redirect_url,
                    recursive_count=recursive_count + 1,
                )
            return redirect_url
        logger.info(f"Got the redirected url={original_url}")
        return original_url
    except requests.exceptions.RequestException as _:
        logger.info(
            f"While getting redirect URL for {original_url} got request issue"
            f"Returning original URL."
        )
        return original_url
    except CustomException as exe:
        logger.info(
            f"FYI: While getting the redirect url, we found that "
            f"url={original_url} is unreachable with reason={exe}"
        )
        return original_url
    # As we want to handle runtime exceptions without pylint issues
    except Exception as error:  # pylint: disable=W0703
        logger.info(
            f"An unexpected error occurred while getting the "
            f"redirect URL for {original_url}: {error}"
        )
        return original_url


def ner_api(text):
    logger.info(f"calculating the ner for the text={text}")
    word_ents_list = []
    if not text:
        return word_ents_list

    url = f"{AZURE_NER_ENDPOINT}/text/analytics/v3.1/entities/recognition/general"
    headers = {
        "Ocp-Apim-Subscription-Key": AZURE_NER_KEY,
        "Content-Type": "application/json",
    }
    documents = [{"id": "1", "text": text}]
    data = {"documents": documents}
    response = requests.post(url, headers=headers, json=data, timeout=20)
    result = response.json()
    result = result.get("documents")
    if len(result) == 0:
        return word_ents_list

    entities = result[0].get("entities")

    for entity in entities:
        word = entity.get("text")
        entity_value = entity.get("category")
        confidence = entity.get("confidenceScore")
        logger.debug(f"{word}, {entity_value}, {confidence}")
        word_ent_map = {"word": word, "entity": entity_value}
        word_ents_list.append(word_ent_map)
    return word_ents_list


def get_og_tags(url: str):
    # TODO: Refactor the code to use constants throughout the code
    logger.info(f"Trying to get the og tags for the url={url}")
    found_tags = {}
    try:
        response = requests.get(url, allow_redirects=False, timeout=5)
        if response.status_code != 200:
            return found_tags
        content_type = response.headers.get("Content-Type", "")
        if "text/html" not in content_type:
            logger.info(f"Unexpected content type: {content_type}")
            return found_tags

        soup = BeautifulSoup(response.content, "html.parser")

        for og_tag in KNOWN_OPENGRAPH_TAGS:
            new_found_tag = soup.find(OGConstants.META.value, property=og_tag)
            if new_found_tag:
                if not (
                    content := new_found_tag.get(OGConstants.CONTENT.value)
                ):
                    logger.info(
                        f"For url={url} og_tag={og_tag} has no content={content}"
                    )
                    continue
                found_tags[new_found_tag[OGConstants.PROPERTY.value]] = content
    except UnicodeDecodeError as exception:
        logger.info(
            f"Unable to get the og tags for the url={url}, because of {exception}"
        )
    except LocationParseError:
        logger.info(
            f"Unable to get the og tags for the url={url}, because of LocationParse"
        )
    except requests.exceptions.RequestException as _:
        logger.info(
            f"Unable to get the og tags for the url={url}, because of request"
        )
    except CustomException as exc:
        logger.info(
            f"Unable to get the og tags for the url={url}, because of {exc}"
        )

    return found_tags


def get_sqs_parsed_event(event):
    new_format = []
    records = event.get("Records", [])
    try:
        for record in records:
            body = record.get("body")
            if body:
                parsed_body = json.loads(body)
                new_format.append(parsed_body)

    except Exception as exc:
        logger.error(
            f"Error while trying to parse the input={json.dumps(event)} with"
            f" error of {exc}"
        )
        raise exc
    return new_format


def subtract_dates(date1: date, date2: date):
    if isinstance(date1, str):
        date1 = datetime.strptime(date1, "%Y-%m-%d")
    if isinstance(date2, str):
        date2 = datetime.strptime(date2, "%Y-%m-%d")

    return (date1 - date2).days
