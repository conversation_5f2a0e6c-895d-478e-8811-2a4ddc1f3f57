import re
from urllib import parse

from bs4 import BeautifulSoup
from bs4 import NavigableString

from utils.entities.alert_entities import AlertEntities
from utils.entities.newsletter_entities import NewsletterNameEntities
from utils.exceptions.exceptions import CustomException
from utils.logger import get_logger
from utils.newsletter import get_newsletter_details

logger = get_logger(__name__)


def extract_inc42_newsletter(html_body: str):
    news = []
    soup = BeautifulSoup(html_body, "html.parser")
    newsletter_block_items = soup.find(class_="text_block block-5")

    if not newsletter_block_items:
        return news

    for newsletter_block_item in newsletter_block_items.find_all("p"):
        if not newsletter_block_item.text.strip():
            continue

        newsletter_block_item_contents = newsletter_block_item.find_all(
            "span"
        )[0].contents
        len_content = len(newsletter_block_item_contents)
        if not len_content >= 3:
            continue

        for j in range(len_content - 1, 0, -1):
            if not newsletter_block_item_contents[j].getText().strip():
                len_content -= 1
            else:
                break

        title_text = ""
        for i in range(0, len_content - 2):
            title_text += newsletter_block_item_contents[i].getText()

        if not re.match(r"^\d", title_text):
            break

        title = title_text[title_text.find("/") + 1 :].strip()[:-1]

        description = ""
        for i in range(len_content):
            if isinstance(newsletter_block_item_contents[i], NavigableString):
                description = newsletter_block_item_contents[i].getText()
        news_link = (
            newsletter_block_item_contents[len_content - 1]
            .find_next("a")
            .get("href")
        )

        if title and description and news_link:
            news.append(
                get_newsletter_details(
                    title=title,
                    description=description,
                    news_link=news_link,
                    news_source=NewsletterNameEntities.INC42.value,
                )
            )

    return news


def extract_arc_newsletter(html_body: str):
    news = []
    soup = BeautifulSoup(html_body, "html.parser")
    try:
        content = (
            soup.find("body")
            .find_all("div", recursive=False)[1]
            .contents[1]
            .find("td")
            .find_all("div", recursive=False)
        )
        len_of_content = len(content)
        if len_of_content <= 0:
            return news
        if len_of_content <= 10:
            title, description = (
                content[2].text.replace("\xa0", " ").split("Arc Notes")
            )
            news_link = content[3].find_all("a")[0].attrs["href"]
            image = content[2].find_all("img")[0].attrs["src"]
            news.append(
                get_newsletter_details(
                    title=title,
                    description=description,
                    news_link=news_link,
                    news_source=NewsletterNameEntities.ARC.value,
                    news_image=image,
                )
            )
        if len_of_content >= 10:
            title, description = (
                content[2].text.replace("\xa0", " ").split("Arc Notes")
            )
            news_link = content[3].find_all("a")[0].attrs["href"]
            image = content[2].find_all("img")[0].attrs["src"]
            news.append(
                get_newsletter_details(
                    title=title,
                    description=description,
                    news_link=news_link,
                    news_source=NewsletterNameEntities.ARC.value,
                    news_image=image,
                )
            )

            title, description = (
                content[7].text.replace("\xa0", " ").split("Arc Notes")
            )
            news_link = content[8].find_all("a")[0].attrs["href"]
            image = content[7].find_all("img")[0].attrs["src"]
            news.append(
                get_newsletter_details(
                    title=title,
                    description=description,
                    news_link=news_link,
                    news_source=NewsletterNameEntities.ARC.value,
                    news_image=image,
                )
            )
    except CustomException as exc:
        print(f"Unable to parse the body={exc}")

    return news


def parse_google_news_alert(
    html_body: str, alert_name: str, alert_subject: str
):
    logger.debug(f"parsing for {html_body}, {alert_name}, {alert_subject}")
    news = []
    soup = BeautifulSoup(html_body, "html.parser")

    company_name = None
    if alert_subject:
        company_name = alert_subject.split(" - ")[-1].split(",")[0]

    articles = soup.find_all(attrs={"itemtype": "http://schema.org/Article"})

    for article in articles:
        title_tag = article.find(itemprop="name")
        title = title_tag.get_text(strip=True) if title_tag else None

        link_tag = article.find(itemprop="url")
        link = link_tag.get("href") if link_tag else None
        if link:
            link = parse.parse_qs(parse.urlparse(link).query).get("url")[0]

        desc_tag = article.find(itemprop="description")
        description = desc_tag.get_text(strip=True) if desc_tag else None

        source_tag = article.find(itemprop="publisher")
        source_name = (
            source_tag.find(itemprop="name").get_text(strip=True)
            if source_tag
            else None
        )

        if title and link and description:
            news_item = get_newsletter_details(
                title=title,
                description=description,
                news_link=link,
                news_source=source_name,
            )
            news_item[AlertEntities.COMPANY_NAME.value] = company_name
            news.append(news_item)

    return news
