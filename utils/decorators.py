import sys
import time
import traceback
import requests

from functools import wraps

from utils.exceptions.exceptions import CustomException


def get_exec_info():
    exec_info = sys.exc_info()
    formatted_exec_info = traceback.format_exception(*exec_info)
    exception_info = ""
    for exc_lines in formatted_exec_info:
        exception_info = exception_info + exc_lines
    return exception_info


def non_api_exception_handler(*decorator_args, **decorator_kwargs):
    logger = decorator_kwargs["logger"]
    logger.debug(f"decorator args: {decorator_args}")

    def decorator(func):
        def wrapper(*args, **kwargs):
            event = kwargs.get("event", args[0])
            now = time.time()

            handler_name = decorator_kwargs.get("handler_name", func.__name__)

            error_message = (
                "Error Reported! \n"
                f"handler: {handler_name} \n"
                f"body: {event} \n"
                "error_description: \n"
            )

            try:
                func_response = func(*args, **kwargs)
                later = time.time()
                difference = int(later - now)
                print(
                    "Time taken for handler name ="
                    f" {handler_name} time={difference} seconds"
                )
                response = func_response
            except CustomException as ex:
                exec_info = get_exec_info()
                exception = error_message + exec_info + str(ex)
                logger.exception(exception)
                response = exception

            return response

        return wrapper

    return decorator



def retry_with_timeouts(**decorator_kwargs):
    logger = decorator_kwargs["logger"]
    timeouts = decorator_kwargs.get("timeouts", [10, 20, 30])
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            for t in timeouts:
                try:
                    return func(*args, timeout=t, **kwargs)
                except requests.Timeout as e:
                    logger.warning(f"Timeout after {t}s. Retrying with longer timeout... Error: {e}")
                except requests.RequestException as e:
                    logger.error(f"Non-timeout request error: {e}")
                    return None
            logger.error("All retry attempts failed. Returning None.")
            return None
        return wrapper
    return decorator