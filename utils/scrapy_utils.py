from fake_useragent import UserAgent


def generate_headers(referer_url):
    user_agent = UserAgent()
    headers = {
        "User-Agent": user_agent.random,
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,"
        "image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.9",
        "Connection": "keep-alive",
        "Referer": referer_url,
    }
    return headers
