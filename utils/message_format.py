class UrlPublishMessageFormat:
    """
    Data model for formatting URL publish messages,
    encapsulating entity_id, parser service, service ID, and URL.
    """

    def __init__(
        self,
        **kwargs,
    ):
        self.entity_id = kwargs.get("entity_id")
        self.parser_service = kwargs.get("parser_service")
        self.service_id = kwargs.get("service_id")
        self.url = kwargs.get("url")
        self.entity_type = kwargs.get("entity_type")

    def to_response(self):
        return {
            "entity_id": self.entity_id,
            "parser_service": self.parser_service,
            "service_id": self.service_id,
            "url": self.url,
            "entity_type": self.entity_type,
        }
