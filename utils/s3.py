import json
from datetime import datetime

import boto3

import config
from utils.entities.collection_entities import FeedUrlEntities
from utils.entities.event_entities import S3Entities
from utils.logger import get_logger

logger = get_logger(__name__)


def write_to_bucket(
    bucket_name,
    object_key,
    body_data,
    s3_client=None,
):
    logger.info(
        "Writing the content to the bucket_name=%s, object_key=%s",
        bucket_name,
        object_key,
    )
    if s3_client is None:
        s3_client = boto3.client("s3")
    object_url = None
    try:
        s3_client.put_object(
            Bucket=bucket_name, Key=object_key, Body=body_data
        )
        object_url = f"s3://{bucket_name}/{object_key}"
    except Exception as exc:
        logger.error(
            "Error occurred while uploading the content to the"
            " bucket=%s for the object_key=%s and"
            " exception =%s",
            bucket_name,
            object_key,
            exc,
        )
        raise exc

    logger.info("Successfully uploaded %s to s3", object_url)
    return object_url


def upload_file(
    bucket_name,
    object_key,
    file_path,
    s3_client=boto3.client("s3"),
):
    logger.info(
        "Writing the content to the bucket_name=%s, object_key=%s",
        bucket_name,
        object_key,
    )
    object_url = None
    try:
        s3_client.upload_file(file_path, bucket_name, object_key)
        object_url = f"s3://{bucket_name}/{object_key}"
    except Exception as exc:
        logger.error(
            "Error occurred while uploading the content to the"
            " bucket=%s for the object_key=%s and"
            " exception =%s",
            bucket_name,
            object_key,
            exc,
        )
        raise exc

    logger.info("Successfully uploaded %s to s3", object_url)
    return object_url


def upload_byte_stream(
    bucket_name,
    object_key,
    file_object,
    s3_client=boto3.client("s3"),
):
    logger.info(
        "Writing the content to the bucket_name=%s, object_key=%s",
        bucket_name,
        object_key,
    )

    try:
        s3_client.upload_fileobj(file_object, bucket_name, object_key)
        object_url = f"s3://{bucket_name}/{object_key}"
    except Exception as exc:
        logger.error(
            "Error occurred while uploading the content to the"
            " bucket=%s for the object_key=%s and"
            " exception =%s",
            bucket_name,
            object_key,
            exc,
        )
        raise exc

    logger.info("Successfully uploaded %s to s3", object_url)
    return object_url


def download_file(
    bucket_name,
    object_key,
    local_path,
    s3_client=boto3.client("s3"),
):
    logger.info(
        "Downloading the content from the bucket_name=%s, object_key=%s to local_path=%s",
        bucket_name,
        object_key,
        local_path,
    )

    try:
        object_url = f"s3://{bucket_name}/{object_key}"
        s3_client.download_file(bucket_name, object_key, local_path)
    except Exception as exc:
        logger.error(
            "Error occurred while downloading the content to the"
            " bucket=%s for the object_key=%s and"
            " exception =%s",
            bucket_name,
            object_key,
            exc,
        )
        raise exc

    logger.info("Successfully downloaded from  %s", object_url)
    return object_url


def create_news_file(s3_message, entity_type, entity_id):
    bucket_name = config.FEEDS_BUCKET
    current_datetime = datetime.now()
    current_date = current_datetime.strftime("%Y-%m-%d")
    current_timestamp = current_datetime.timestamp()
    object_key = (
        f"{S3Entities.NEWS_FOLDER.value}/{entity_type}_news/"
        f"{current_date}/{entity_id}_{current_timestamp}.json"
    )
    write_to_bucket(
        bucket_name=bucket_name,
        object_key=object_key,
        body_data=json.dumps(s3_message),
    )
    message_data = {
        FeedUrlEntities.S3_BUCKET_NAME.value: bucket_name,
        FeedUrlEntities.S3_OBJECT_KEY.value: object_key,
    }
    return message_data
