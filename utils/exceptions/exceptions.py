class CustomException(Exception):
    """
    For exceptions thrown by the application
    """

    error_message = "ERROR"
    status_code = 0
    error_detail = None

    def __init__(self, error_detail):
        CustomException.error_detail = error_detail
        super().__init__({})


class DiffbotURINotPresent(Exception):
    """When diffbot URI not present while fetching news"""

    error_message = "Diffbot URI not present"
    status_code = 0
    error_detail = None

    def __init__(self, error_detail):
        CustomException.error_detail = error_detail
        super().__init__({})


class NewsdataAPIException(Exception):
    """When newsdata API call fails"""

    error_message = "Newsdata API call failed"
    status_code = 0
    error_detail = None

    def __init__(self, error_detail):
        CustomException.error_detail = error_detail
        super().__init__({})
