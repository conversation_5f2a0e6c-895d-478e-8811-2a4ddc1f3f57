# Python Hooks
repos:
   - repo: local
     hooks:
       - id: pylint
         name: pylint
         entry: pylint --rcfile=.pylintrc --ignore=testcase
         language: python
         types: [ python ]
         verbose: true
       - id: pytest-check
         name: pytest-check
         entry: coverage run -m pytest
         language: system
         pass_filenames: false
         always_run: true
         verbose: true
       - id: coverage report
         name: coverage report
         entry: coverage report --show-missing --fail-under=77
         language: system
         pass_filenames: false
         always_run: true
         verbose: true