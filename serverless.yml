frameworkVersion: "3"
plugins:
  - serverless-offline
  - serverless-prune-plugin
  - serverless-plugin-log-subscription
  - serverless-latest-layer-version
  - serverless-plugin-utils

service: ${self:custom.service.name}

custom:
  prune:
    automatic: true
    includeLayers: true
    number: 3
  enabled:
    prod: true
    other: false
  defaultLayers: ${split(${file(./config.json):LAYERS_ARN}, ',')}
  defaultVpc:
    securityGroupIds:
      - ${file(./config.json):SG1}
    subnetIds:
      - ${file(./config.json):SUBNET1}
      - ${file(./config.json):SUBNET2}
  defaultCors:
    origin: ${file(./config.json):ORIGIN}
    headers:
      - Content-Type
      - X-Amz-Date
      - Authorization
      - X-Api-Key
      - X-Amz-Security-Token
      - X-Amz-User-Agent
      - x-requested-with
  prefix: ${self:provider.stage}
  awsService: "lambda"
  productService: "rss-feed-parser"
  product: ${file(./config.json):PRODUCT}
  company: ${file(./config.json):COMPANY}
  service:
    label: ${self:custom.prefix}-${self:provider.stackTags.PRODUCT}
    name: ${self:custom.prefix}-${self:provider.stackTags.PRODUCT}-cf-${self:custom.productService}
  defaultMessageRetentionPeriod: 1037000
  logSubscription:
    enabled: true
    destinationArn: ${file(./config.json):ALERTING_LAMBDA_ARN}
    filterPattern: ${file(./config.json):SUBSCRIPTION_FILTER_PATTERN}
    addLambdaPermission: false

provider:
  name: aws
  runtime: python3.12
  stackName: ${self:custom.service.name}
  stackTags:
    COMPANY: ${self:custom.company}
    ENVIRONMENT: ${opt:stage,'dev'}
    OWNER: ${file(./config.json):OWNER}
    PRODUCT: ${self:custom.product}
    AWS_SERVICE: ${self:custom.awsService}
  lambdaHashingVersion: 20201221
  memorySize: ${file(./config.json):MEMORY_SIZE}
  timeout: ${file(./config.json):TIMEOUT}
  region: ${file(./config.json):REGION}
  stage: ${opt:stage,'dev'}
  logRetentionInDays: 30
  deploymentBucket:
    name: ${file(./config.json):DEPLOYMENT_BUCKET} # Deployment bucket name. Default is generated by the framework
    serverSideEncryption: AES256 # when using server-side encryption
    tags: # Tags that will be added to each of the deployment resources
      environment: ${self:provider.stage}
      service: lambda-layer
      owner: ${file(./config.json):OWNER}
  deploymentPrefix: serverless
  tracing:
    lambda: true
    apiGateway: true
  logs:
    restApi: true
  apiGateway:
    disableDefaultEndpoint: false

resources:
  Resources:

    NewsFeedQueue:
      Type: AWS::SQS::Queue
      Properties:
        FifoQueue: true
        ContentBasedDeduplication: true
        QueueName: ${self:custom.service.label}-sqs-news-feed.fifo
        VisibilityTimeout: 902
        ReceiveMessageWaitTimeSeconds: 20
        MessageRetentionPeriod: ${self:custom.defaultMessageRetentionPeriod}
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - NewsFeedDLQQueue
              - Arn
          maxReceiveCount: 2

    NewsFeedQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Id: NewsFeedQueue
          Statement:
            - Sid: AllowLambda
              Effect: Allow
              Principal: "*"
              Action:
                - sqs:SendMessage
              Resource:
                Fn::GetAtt:
                  - NewsFeedQueue
                  - Arn
        Queues:
          - Ref: NewsFeedQueue

    NewsFeedDLQQueue:
      Type: AWS::SQS::Queue
      Properties:
        FifoQueue: true
        ContentBasedDeduplication: true
        QueueName: ${self:custom.service.label}-sqs-news-feed-dlq.fifo
        VisibilityTimeout: 60
        ReceiveMessageWaitTimeSeconds: 20
        MessageRetentionPeriod: 604800

    NewsFeedDLQQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Id: NewsFeedDLQQueue
          Statement:
            - Sid: Allow-SQS-SendMessage
              Effect: Allow
              Principal: "*"
              Action:
                - sqs:SendMessage
              Resource:
                Fn::GetAtt:
                  - NewsFeedDLQQueue
                  - Arn
              Condition:
                ArnEquals:
                  aws:SourceArn:
                    Fn::GetAtt:
                      - NewsFeedDLQQueue
                      - Arn
        Queues:
          - Ref: NewsFeedDLQQueue

    InsertNewsFeedQueue:
      Type: AWS::SQS::Queue
      Properties:
        FifoQueue: true
        ContentBasedDeduplication: true
        QueueName: ${self:custom.service.label}-sqs-insert-news-feed.fifo
        VisibilityTimeout: 60
        ReceiveMessageWaitTimeSeconds: 20
        MessageRetentionPeriod: ${self:custom.defaultMessageRetentionPeriod}
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - InsertNewsFeedDLQQueue
              - Arn
          maxReceiveCount: 2

    InsertNewsFeedQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Id: InsertNewsFeedQueue
          Statement:
            - Sid: AllowLambda
              Effect: Allow
              Principal: "*"
              Action:
                - sqs:SendMessage
              Resource:
                Fn::GetAtt:
                  - InsertNewsFeedQueue
                  - Arn
        Queues:
          - Ref: InsertNewsFeedQueue

    InsertNewsFeedDLQQueue:
      Type: AWS::SQS::Queue
      Properties:
        FifoQueue: true
        ContentBasedDeduplication: true
        QueueName: ${self:custom.service.label}-sqs-insert-news-feed-dlq.fifo
        VisibilityTimeout: 60
        ReceiveMessageWaitTimeSeconds: 20
        MessageRetentionPeriod: 604800

    InsertNewsFeedDLQQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Id: InsertNewsFeedDLQQueue
          Statement:
            - Sid: Allow-SQS-SendMessage
              Effect: Allow
              Principal: "*"
              Action:
                - sqs:SendMessage
              Resource:
                Fn::GetAtt:
                  - InsertNewsFeedDLQQueue
                  - Arn
              Condition:
                ArnEquals:
                  aws:SourceArn:
                    Fn::GetAtt:
                      - InsertNewsFeedQueue
                      - Arn
        Queues:
          - Ref: InsertNewsFeedDLQQueue

    WeblinkToPDFConverterRequestQueue:
      Type: AWS::SQS::Queue
      Properties:
        FifoQueue: true
        ContentBasedDeduplication: true
        QueueName: ${self:custom.service.label}-sqs-weblink-to-pdf-converter-request.fifo
        VisibilityTimeout: 600
        ReceiveMessageWaitTimeSeconds: 20
        MessageRetentionPeriod: ${self:custom.defaultMessageRetentionPeriod}
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - WeblinkToPDFConverterRequestDLQQueue
              - Arn
          maxReceiveCount: 3

    WeblinkToPDFConverterRequestQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Id: WeblinkToPDFConverterRequestQueue
          Statement:
            - Sid: AllowLambda
              Effect: Allow
              Principal: "*"
              Action:
                - sqs:SendMessage
              Resource:
                Fn::GetAtt:
                  - WeblinkToPDFConverterRequestQueue
                  - Arn
        Queues:
          - Ref: WeblinkToPDFConverterRequestQueue

    WeblinkToPDFConverterRequestDLQQueue:
      Type: AWS::SQS::Queue
      Properties:
        FifoQueue: true
        ContentBasedDeduplication: true
        QueueName: ${self:custom.service.label}-weblink-to-pdf-converter-request-dlq.fifo
        VisibilityTimeout: 60
        ReceiveMessageWaitTimeSeconds: 20
        MessageRetentionPeriod: 604800

    WeblinkToPDFConverterRequestDLQQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Id: WeblinkToPDFConverterRequestDLQQueue
          Statement:
            - Sid: Allow-SQS-SendMessage
              Effect: Allow
              Principal: "*"
              Action:
                - sqs:SendMessage
              Resource:
                Fn::GetAtt:
                  - WeblinkToPDFConverterRequestDLQQueue
                  - Arn
              Condition:
                ArnEquals:
                  aws:SourceArn:
                    Fn::GetAtt:
                      - WeblinkToPDFConverterRequestQueue
                      - Arn
        Queues:
          - Ref: WeblinkToPDFConverterRequestDLQQueue

    AltURLFeedFetcherQueue:
      Type: AWS::SQS::Queue
      Properties:
        FifoQueue: true
        ContentBasedDeduplication: true
        QueueName: ${self:custom.service.label}-sqs-alt-url-feed-fethcer.fifo
        VisibilityTimeout: 600
        ReceiveMessageWaitTimeSeconds: 20
        MessageRetentionPeriod: ${self:custom.defaultMessageRetentionPeriod}
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - AltURLFeedFetcherDLQQueue
              - Arn
          maxReceiveCount: 3

    AltURLFeedFetcherQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Id: AltURLFeedFetcherQueue
          Statement:
            - Sid: AllowLambda
              Effect: Allow
              Principal: "*"
              Action:
                - sqs:SendMessage
              Resource:
                Fn::GetAtt:
                  - AltURLFeedFetcherQueue
                  - Arn
        Queues:
          - Ref: AltURLFeedFetcherQueue

    AltURLFeedFetcherDLQQueue:
      Type: AWS::SQS::Queue
      Properties:
        FifoQueue: true
        ContentBasedDeduplication: true
        QueueName: ${self:custom.service.label}-sqs-alt-url-feed-fethcer-dlq.fifo
        VisibilityTimeout: 60
        ReceiveMessageWaitTimeSeconds: 20
        MessageRetentionPeriod: 604800

    AltURLFeedFetcherDLQQueuePolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Id: AltURLFeedFetcherDLQQueue
          Statement:
            - Sid: Allow-SQS-SendMessage
              Effect: Allow
              Principal: "*"
              Action:
                - sqs:SendMessage
              Resource:
                Fn::GetAtt:
                  - AltURLFeedFetcherDLQQueue
                  - Arn
              Condition:
                ArnEquals:
                  aws:SourceArn:
                    Fn::GetAtt:
                      - AltURLFeedFetcherQueue
                      - Arn
        Queues:
          - Ref: AltURLFeedFetcherDLQQueue

functions:

  generate-report:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-generate-report
    handler: handlers/report_handler.generate_report
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}
    timeout: 30
    memorySize: 1024
    events:
      - schedule:
          name: ${self:custom.service.label}-rule-report-generation
          description: Publish raw and denoised news reports and publish it
          rate: rate(1 day)


  news-feeds-invoker:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-news-feeds-invoker
    handler: handlers/ds_news_feed_handler.news_feed_invoker_handler
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}
    timeout: 60
    events:
      - schedule:
          name: ${self:custom.service.label}-rule-news-feeds-invoker
          description: Cron job that runs once every fifteen minutes
          rate: rate(30 minutes)
          enabled: ${file(./config.json):ENABLE_NEWSLETTER_JOB}

  get-news-feeds:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-get-news-feed
    handler: handlers/get_company_news_feeds_handler.get_company_news_feeds_handler
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    timeout: 900
    layers: ${self:custom.defaultLayers}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - NewsFeedQueue
              - Arn
          batchSize: 1
          maximumConcurrency: 10

  republish-news-feed:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-republish-news
    handler: handlers/republish_news_feeds.republish_news_feed_handler
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}

  rss-feeds-parser:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-rss-feeds-parser
    handler: handlers/ds_rss_news_feed_handler.ds_rss_feed_handler
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}
    timeout: 200
    events:
      - schedule:
          name: ${self:custom.service.label}-rss-feeds-parser
          description: Cron job that runs once every day
          rate: rate(1 day)
  run-newsletter-job:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-run-newsletter-job
    handler: handlers/newsletter_handlers.run_newsletter_handler
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}
    timeout: 900
    events:
      - schedule:
          name: ${self:custom.service.label}-run-newsletter-job
          description: Cron job that runs the newsletter job
          rate: rate(12 hours)
          enabled: ${file(./config.json):ENABLE_NEWSLETTER_JOB}
          input:
            input_event_file_name: "scheduler/newsletter_configs.json"

  run-mail-news-job:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-run-mail-news-job
    handler: handlers/mail_news_handlers.run_mail_news_handler
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}
    timeout: 600
    events:
      - schedule:
          name: ${self:custom.service.label}-run-google-alerts-job
          description: Cron job that runs the google mail alerts job
          rate: rate(10 minutes)
          enabled: ${file(./config.json):ENABLE_GOOGLE_ALERTS_JOB}
          input:
            input_event_file_name: "scheduler/news_alerts_configs.json"

  mongo-duplicate-cleanup:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-mongo-cleanup
    handler: handlers/duplicate_cleanup_handler.duplicate_cleanup_handler
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}

  insert-feed:
    name: ${self:custom.service.label}-${self:custom.awsService}-insert-feed
    description: Insert the news feed in raw collection and send the data to denoiser
    handler: handlers/insert_feed_handler.insert_feed_handler
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}
    timeout: 58
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - InsertNewsFeedQueue
              - Arn
          batchSize: 1
          maximumConcurrency: 2

  weblink-to-pdf-converter:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-weblink-to-pdf-converter
    image: ${file(./config.json):WEB_LINK_TO_PDF_CONVERTER_IMAGE}
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    timeout: 600
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - WeblinkToPDFConverterRequestQueue
              - Arn
          batchSize: 1
          maximumConcurrency: 5

  alt-feed-preprocessor:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-alt-feed-preprocessor
    handler: handlers/alt_feed_handlers.run_alt_feed_handler
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}
    timeout: 300

  alt-feed-url-fetcher:
    name: ${self:custom.prefix}-${self:custom.product}-${self:custom.awsService}-alt-feed-url-fetcher
    handler: handlers/alt_feed_handlers.fetch_alt_feed_url_feed
    role: ${file(./config.json):ROLE_ARN}
    vpc: ${self:custom.defaultVpc}
    layers: ${self:custom.defaultLayers}
    timeout: 300

package:
  patterns:
    - "!.circleci/**"
    - "!.gitignore/**"
    - "!.serverless/**"
    - "!requirements.txt"
    - "!venv/**"
    - "!serverless.yml"
    - "!test_cases/**"
    - "!tests/**"
    - "!repository/**"
    - "!Readme.md"
    - "!package.json"
    - "!Dockerfile"
    - "!License"
    - "!node_modules/**"
    - "!**/__pycache__/**"
    - "!**/*.pyc  "
    - "!testcases"
