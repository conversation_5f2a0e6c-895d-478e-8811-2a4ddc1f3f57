import datetime
import json

import boto3
from bson import json_util
from pymongo import MongoClient

import config
from constants.entities import CredentialConstants
from utils.logger import get_logger
from utils.s3 import write_to_bucket

logger = get_logger(__name__)

credentials = config.CREDENTIALS.get(config.NEWS_FEEDS["DATABASE"])


class DuplicateCheck:
    """
    This class is used to compare and delete duplicate news feed based on news_links and title.
    """

    def __init__(self):
        self.s3_client = boto3.client("s3")
        self.mongo_client = MongoClient(
            credentials.get("DOCUMENT_DB_URI"),
            connectTimeoutMS=credentials.get(
                CredentialConstants.CONN_TIMEOUT.value
            ),
            serverSelectionTimeoutMS=credentials.get(
                CredentialConstants.CONN_TIMEOUT.value
            ),
        )
        self.database = self.mongo_client[credentials.get("DATABASE")]
        self.collection = self.database[config.FEED_URL_COLLECTION]

    def _backup_to_s3(self, duplicates, bucket_name):
        all_duplicates_to_delete = []

        for group in duplicates:
            documents = group.get("documents", [])
            if len(documents) > 1:
                keep_id = self._select_document_to_keep(documents)
                duplicates_to_delete = [
                    doc for doc in documents if doc["_id"] != keep_id
                ]
                all_duplicates_to_delete.extend(duplicates_to_delete)

        timestamp = datetime.datetime.now().strftime("%Y%m%d%H%M%S")
        file_key = f"duplicate_check_{timestamp}.json"

        data = json.dumps(all_duplicates_to_delete, default=json_util.default)
        write_to_bucket(bucket_name, file_key, data, self.s3_client)
        logger.info(f"Backed up all duplicates to S3: {file_key}")

    def _get_ids_to_delete(self, duplicates):
        ids_to_delete = []
        for group in duplicates:
            documents = group.get("documents", [])
            if len(documents) > 1:
                keep_id = self._select_document_to_keep(documents)
                unique_links = {doc["news_link"] for doc in documents}
                unique_titles = {doc["raw_title"] for doc in documents}
                if len(unique_links) > 1 or len(unique_titles) > 1:
                    continue
                for doc in documents:
                    if doc["_id"] != keep_id:
                        ids_to_delete.append(str(doc["_id"]))
        return ids_to_delete

    def _select_document_to_keep(self, documents):
        docs_with_image = [doc for doc in documents if doc.get("image_link")]
        if docs_with_image:
            return docs_with_image[0]["_id"]

        parser_priority = ["google alert", "bing news", "bing search"]
        for priority in parser_priority:
            for doc in documents:
                if doc.get("parser_service") == priority:
                    return doc["_id"]

        return documents[0]["_id"]

    def process_and_backup_duplicates(self, bucket_name):
        duplicates = list(
            self.collection.aggregate(
                config.DUPLICATE_CHECK_QUERY, allowDiskUse=True
            )
        )
        self._backup_to_s3(duplicates, bucket_name)
        ids_to_delete = self._get_ids_to_delete(duplicates)
        return ids_to_delete

    def delete_duplicates(self, delete_ids):
        batch_size = 100
        result = None
        for i in range(0, len(delete_ids), batch_size):
            batch_ids = delete_ids[i : i + batch_size]
            result = self.collection.delete_many({"_id": {"$in": batch_ids}})
            logger.info(
                f"Deleted {result.deleted_count} documents in batch {i // batch_size + 1}."
            )

        return result
