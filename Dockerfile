FROM --platform=linux/amd64 umihico/aws-lambda-selenium-python:3.10.12

RUN yum install -y \
    gnupg \
    lsb-release \
    wget \
    xvfb \
    libxi6 \
    libgconf-2-4 \
    libXrender \
    libXext \
    libX11 \
    fontconfig \
    freetype

RUN yum install -y openssl xorg-x11-fonts-100dpi xorg-x11-fonts-75dpi xorg-x11-fonts-Type1

RUN wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox-0.12.6-1.centos7.x86_64.rpm && \
     yum localinstall -y wkhtmltox-0.12.6-1.centos7.x86_64.rpm && \
     rm wkhtmltox-0.12.6-1.centos7.x86_64.rpm


RUN pip install pdfkit
RUN pip install webdriver_manager
RUN pip install pre-commit==3.3.3
RUN pip install pylint==2.17.5
RUN pip install pymongo==4.4.1
RUN pip install pytest==7.4.0
RUN pip install coverage==7.2.7
RUN pip install ruamel.yaml==0.17.32
RUN pip install boto3==1.26.145
RUN pip install pycryptodome==3.18.0
RUN pip install requests==2.31.0
RUN pip install pymysql==1.0.2
RUN pip install feedparser==6.0.10
RUN pip install bs4==0.0.1
RUN pip install requests==2.28.2
RUN pip install pymongo==4.3.3
RUN pip install boto3==1.26.140
RUN pip install botocore==1.29.140
RUN pip install undetected-chromedriver==2.1.1
RUN pip install --upgrade awscli


COPY ./ ./

CMD ["handlers.weblink_to_pdf_handler.weblink_to_pdf_handler"]
