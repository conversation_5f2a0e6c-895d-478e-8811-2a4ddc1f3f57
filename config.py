import json
import os
from pathlib import Path


PJT_ROOT_PATH = str(Path(__file__).parent)
SECRET_FILE = PJT_ROOT_PATH + os.sep + "secrets.json"

with open(SECRET_FILE, encoding="utf-8") as f:
    secrets = json.load(f)

FEED_URL_COLLECTION = "feedURL"

NEWSDATA_TOKENS = os.getenv("NEWSDATA_TOKENS", secrets["NEWSDATA_TOKENS"])
NEWSDATA_ENDPOINT = os.getenv(
    "NEWSDATA_ENDPOINT", secrets["NEWSDATA_ENDPOINT"]
)
DS_RSS_FEEDS = os.getenv("DS_RSS_FEEDS", secrets["DS_RSS_FEEDS"])
NEWS_FEEDS = os.getenv("NEWS_FEEDS", secrets["NEWS_FEEDS"])
DEFAULT_TIMEOUT_MS = int(
    os.getenv("DEFAULT_TIMEOUT_MS", secrets["DEFAULT_TIMEOUT_MS"])
)
CREDENTIALS = os.getenv("CREDENTIALS", secrets["CREDENTIALS"])
FEED_SOURCES_TO_IGNORE = os.getenv(
    "FEED_SOURCES_TO_IGNORE", secrets["FEED_SOURCES_TO_IGNORE"]
)
DENOISING_SQS_URL = os.getenv(
    "DENOISING_SQS_URL", secrets["DENOISING_SQS_URL"]
)
DENOISING_URL_BATCH_SIZE = int(
    os.getenv("DENOISING_URL_BATCH_SIZE", secrets["DENOISING_URL_BATCH_SIZE"])
)
AUDITING_SQS_URL = os.getenv("AUDITING_SQS_URL", secrets["AUDITING_SQS_URL"])
WEBHOOK_URL = os.getenv("WEBHOOK_URL", secrets["WEBHOOK_URL"])
NOTIFICATION_LAMBDA_ARN = os.getenv(
    "NOTIFICATION_LAMBDA_ARN", secrets["NOTIFICATION_LAMBDA_ARN"]
)
REPUBLISH_NEWS_ARN = os.getenv(
    "REPUBLISH_NEWS_ARN", secrets["REPUBLISH_NEWS_ARN"]
)
BING_FEED_QUERY_PARAMS = os.getenv(
    "BING_FEED_QUERY_PARAMS", secrets["BING_FEED_QUERY_PARAMS"]
)
DATE_FORMAT = os.getenv("DATE_FORMAT", secrets["DATE_FORMAT"])
INCORPORATION_DATE_FORMAT = os.getenv(
    "INCORPORATION_DATE_FORMAT", secrets["INCORPORATION_DATE_FORMAT"]
)
GOOGLE_BASE_URL = os.getenv("GOOGLE_BASE_URL", secrets["GOOGLE_BASE_URL"])
GOOGLE_NEWS_URL = os.getenv("GOOGLE_NEWS_URL", secrets["GOOGLE_NEWS_URL"])
GOOGLE_NEWS_END_DATE = os.getenv(
    "GOOGLE_NEWS_END_DATE", secrets["GOOGLE_NEWS_END_DATE"]
)
GOOGLE_NEWS_AUX_PARAMS = os.getenv(
    "GOOGLE_NEWS_AUX_PARAMS", secrets["GOOGLE_NEWS_AUX_PARAMS"]
)
DUPLICATE_CHECK_QUERY = os.getenv(
    "DUPLICATE_CHECK_QUERY", secrets["DUPLICATE_CHECK_QUERY"]
)
DUPLICATE_S3_BACKUP_BUCKET = os.getenv(
    "DUPLICATE_S3_BACKUP_BUCKET", secrets["DUPLICATE_S3_BACKUP_BUCKET"]
)
FEEDS_BUCKET = os.getenv("FEEDS_BUCKET", secrets["FEEDS_BUCKET"])
AZURE_NER_KEY = os.getenv("AZURE_NER_KEY", secrets["AZURE_NER_KEY"])
AZURE_NER_ENDPOINT = os.getenv(
    "AZURE_NER_ENDPOINT", secrets["AZURE_NER_ENDPOINT"]
)
NEWS_FEED_SQS_URL = os.getenv(
    "NEWS_FEED_SQS_URL", secrets["NEWS_FEED_SQS_URL"]
)
INSERT_FEED_SERVICE_SQS_URL = os.getenv(
    "INSERT_FEED_SERVICE_SQS_URL", secrets["INSERT_FEED_SERVICE_SQS_URL"]
)
WEBLINK_CONVERTED_PDF_QUEUE_URL = os.getenv(
    "WEBLINK_CONVERTED_PDF_QUEUE_URL",
    secrets["WEBLINK_CONVERTED_PDF_QUEUE_URL"],
)
ALT_FEED_PROCESSOR_SQS_URL = os.getenv(
    "ALT_FEED_PROCESSOR_SQS_URL", secrets["ALT_FEED_PROCESSOR_SQS_URL"]
)
DIRECT_WEBLINK_PROCESSOR_SQS_URL = os.getenv(
    "DIRECT_WEBLINK_PROCESSOR_SQS_URL",
    secrets["DIRECT_WEBLINK_PROCESSOR_SQS_URL"],
)
GMAIL_NEWS_SIZE: int = int(
    os.getenv("GMAIL_NEWS_SIZE", secrets["GMAIL_NEWS_SIZE"])
)
NEWS_FEED_QUEUE_BATCH_SIZE = int(
    os.getenv(
        "NEWS_FEED_QUEUE_BATCH_SIZE", secrets["NEWS_FEED_QUEUE_BATCH_SIZE"]
    )
)
INSERT_NEWS_FEED_QUEUE_BATCH_SIZE = int(
    os.getenv(
        "INSERT_NEWS_FEED_QUEUE_BATCH_SIZE",
        secrets["INSERT_NEWS_FEED_QUEUE_BATCH_SIZE"],
    )
)
NEWS_PARSER_SERVICE_CONFIG = os.getenv(
    "NEWS_PARSER_SERVICE_CONFIG", secrets["NEWS_PARSER_SERVICE_CONFIG"]
)
NEWS_FEED_SOURCES = os.getenv(
    "NEWS_FEED_SOURCES",
    secrets["NEWS_FEED_SOURCES"],
)
PAGINATE_BING = bool(
    os.getenv(
        "PAGINATE_BING",
        secrets["PAGINATE_BING"],
    )
)
ADMIN_ORG_ID = os.getenv(
    "ADMIN_ORG_ID",
    secrets["ADMIN_ORG_ID"],
)
ADMIN_USER_ID = os.getenv(
    "ADMIN_USER_ID",
    secrets["ADMIN_USER_ID"],
)
NEWS_FETCH_BUFFER_HOURS = int(
    os.getenv("NEWS_FETCH_BUFFER_HOURS", secrets["NEWS_FETCH_BUFFER_HOURS"])
)
DIFFBOT_URL = os.getenv("DIFFBOT_URL", secrets["DIFFBOT_URL"])
DIFFBOT_TOKENS = os.getenv("DIFFBOT_TOKENS", secrets["DIFFBOT_TOKENS"])
MAX_DIFFBOT_ARTICLES_TO_FETCH = int(
    os.getenv(
        "MAX_DIFFBOT_ARTICLES_TO_FETCH",
        secrets["MAX_DIFFBOT_ARTICLES_TO_FETCH"],
    )
)

NEWS_REPORT_CONSOLE_URL = (
    f"https://ap-south-1.console.aws.amazon.com/s3/object/{FEEDS_BUCKET}"
    f"?region=ap-south-1&bucketType=general&prefix="
)
