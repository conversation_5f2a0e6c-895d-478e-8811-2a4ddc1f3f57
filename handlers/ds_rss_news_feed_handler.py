import json
import sys
import traceback

sys.path.append("/opt")
from utils.logger import get_logger
from utils.exceptions.exceptions import CustomException
from config import CREDENTIALS
from service.ds_rss_feed_parser.ds_rss_parser import DSRSSParser

logger = get_logger(__name__)


def ds_rss_feed_handler(event, context):
    logger.debug(
        f"Starting Lambda run for event={event} and context={context}"
    )
    try:
        status_code = 200
        message = "Completed"

        parser = DSRSSParser(CREDENTIALS["NEWS_NOSQL"])
        parser.execute_get_rss_feed()
    except CustomException as error:
        status_code = 500
        message = f"RSS News feed parser failed with error {repr(error)}"
        traceback.print_exc()
        logger.error(f"Processing news failed with error {repr(error)}")

    return {"statusCode": status_code, "body": json.dumps(message)}
