import sys
from datetime import datetime
from datetime import timedelta

sys.path.append("/opt")

from service.report_service import ReportService
from utils.logger import get_logger

logger = get_logger(__name__)

report_service = ReportService()


def generate_report(event, context):
    logger.debug(
        f"Started the report job for event={event} and context={context}"
    )
    start_date = event.get("start_date")
    end_date = event.get("end_date")

    if not start_date or not end_date:
        logger.info(
            f"No start date={start_date} or end_date={end_date}"
            f" given, hence generating report for yesterday"
        )
        start_date = end_date = (
            datetime.today() - timedelta(days=1)
        ).strftime("%Y-%m-%d")

    report_service.generate_news_report(
        start_date_str=start_date, end_date_str=end_date
    )
    logger.info("Finished report generation")
