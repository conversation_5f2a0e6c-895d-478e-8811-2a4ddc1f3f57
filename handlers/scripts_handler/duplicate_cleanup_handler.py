import json
import sys
import traceback


sys.path.append("/opt")

from utils.logger import get_logger
from utils.exceptions.exceptions import CustomException
from scripts.duplicate_check_and_delete import DuplicateCheck
from config import DUPLICATE_S3_BACKUP_BUCKET

duplicate_check = DuplicateCheck()
logger = get_logger(__name__)


def duplicate_cleanup_handler(event, context):
    status_code = 200
    message = "Completed"
    response = None
    try:
        if event is None:
            logger.error("empty event received")
            return "Bad Request"

        logger.debug(
            f"Duplicate Check handler has received event ={event} and context={context}"
        )
        delete_ids = duplicate_check.process_and_backup_duplicates(
            DUPLICATE_S3_BACKUP_BUCKET
        )
        delete_flag = event.get("to_delete")
        if delete_flag:
            response = duplicate_check.delete_duplicates(delete_ids)
            logger.info("Finished deleting the duplicates")
        else:
            logger.info("Duplicate check completed without deletion")
        logger.info("Finished processing the event")

    except CustomException as _e:
        traceback.print_exc()
        status_code = 500
        message = f"Duplicate check failed with error {repr(_e)}"
        logger.error("Duplicate check failed with an exception %s", repr(_e))

    return {
        "statusCode": status_code,
        "data": response,
        "message": json.dumps(message),
    }
