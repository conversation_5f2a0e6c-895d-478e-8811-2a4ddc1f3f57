import sys

sys.path.append("/opt")

from service.alt_feed_fetcher_service import AltFeedService
from utils.decorators import non_api_exception_handler
from utils.logger import get_logger

logger = get_logger(__name__)

alt_feed_service = AltFeedService()


@non_api_exception_handler(logger=logger)
def run_alt_feed_handler(event, context):
    logger.debug(
        f"Running the alt feed parser for the event={event} and context={context}"
    )

    alt_feed_service.initiate_alt_feed_processor()

    logger.info("Successfully ran the alt feed runner")


@non_api_exception_handler(logger=logger)
def fetch_alt_feed_url_feed(event, context):
    logger.debug(
        f"Running the alt feed fetcher for the event={event} and context={context}"
    )

    url = event.get("alt_feed_url")

    alt_feed_service.process_and_scrape(link_url=url)

    logger.info("Successfully ran the alt feed fetcher")
