import sys

from utils.general_utils import get_sqs_parsed_event
from utils.logger import get_logger

sys.path.append("/opt")
from service.weblink_to_pdf_orchestrator_service import (
    WeblinkToPdfOrchestratorService,
)

logger = get_logger(__name__)


def weblink_to_pdf_handler(event, context):
    logger.info(
        f"Received pdf event={event} and context={context} for download"
    )

    formatted_events = get_sqs_parsed_event(event)
    for formatted_event in formatted_events:

        logger.info("parsing event %s", formatted_event)

        if (
            "file_storage_path" not in formatted_event
            or not formatted_event.get("file_storage_path")
        ):
            raise ValueError("File storage path not found")
        if "weblink" not in formatted_event or not formatted_event.get(
            "weblink"
        ):
            raise ValueError("No weblink found")
        if "bucket_name" not in formatted_event or not formatted_event.get(
            "bucket_name"
        ):
            raise ValueError("No bucket_name found")
        if "title" not in formatted_event or not formatted_event.get("title"):
            raise ValueError("No title found")

        file_storage_path = formatted_event.get("file_storage_path")
        weblink = formatted_event.get("weblink")
        bucket_name = formatted_event.get("bucket_name")
        title = formatted_event.get("title")
        theme_ids = formatted_event.get("theme_ids")
        message_trace_id = formatted_event.get("message_trace_id")
        org_id = formatted_event.get("org_id")
        user_id = formatted_event.get("user_id")
        document_type = formatted_event.get("document_type")

        weblink_to_pdf_orchestrator_service = WeblinkToPdfOrchestratorService()
        weblink_to_pdf_orchestrator_service.convert_weblink_and_upload(
            weblink=weblink,
            bucket_name=bucket_name,
            title=title,
            file_storage_path=file_storage_path,
            theme_ids=theme_ids,
            message_trace_id=message_trace_id,
            org_id=org_id,
            user_id=user_id,
            document_type=document_type,
        )
