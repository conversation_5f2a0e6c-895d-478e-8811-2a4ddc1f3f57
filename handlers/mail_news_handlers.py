import json
import sys

sys.path.append("/opt")
from utils.logger import get_logger
from utils.decorators import non_api_exception_handler
from service.mail_news_service import MailNewsService

logger = get_logger(__name__)


@non_api_exception_handler(logger=logger)
def run_mail_news_handler(event, context):
    logger.debug(
        f"Running the news alert for the event={event} and context={context}"
    )

    file_name = event.get("input_event_file_name")
    if not file_name:
        logger.info(f"Loading the input data from the filename={file_name} ")
        return ["Input filename missing"]

    with open(file_name, "r", encoding="utf-8") as file:
        json_config = json.load(file)

        news_alert_configs = json_config.get("news_alert_configs")
        if not news_alert_configs or len(news_alert_configs) == 0:
            logger.info("No newsletter config found, so stopping the process")
            return []

        MailNewsService().run_mail_news(alert_configs=news_alert_configs)

        logger.info("Successfully ran the job for the newsletter")
        return []
