import sys

sys.path.append("/opt")
import json
from utils.date_utils import str_to_datetime
from utils.general_utils import get_sqs_parsed_event

from utils.logger import get_logger
from config import NEWS_FEEDS, CREDENTIALS
from constants.api_parameters import APIParameters
from service.ds_rss_feed_parser.get_company_news_feed import GetCompanyNewsFeed

logger = get_logger(__name__)


def get_company_news_feeds_handler(event, context):
    logger.debug(
        f"Starting Lambda run for the event={json.dumps(event)} and context={context}"
    )

    records = get_sqs_parsed_event(event)
    for index, record in enumerate(records):
        company_cin = record.get(APIParameters.COMPANY_CIN_FIELD.value)
        feed_source = record.get(APIParameters.FEED_SOURCE.value)
        service_id = record.get(APIParameters.AUDIT_UUID_FIELD.value)

        if not company_cin or not feed_source or not service_id:
            logger.error(
                f"Received bad request, either company_cin={company_cin}, "
                f"feed_source={feed_source}, "
                f"service_id={service_id} is/are empty for "
                f"the record_index={index}"
            )
            continue

        republish_after = record.get(APIParameters.RE_PUBLISH_AFTER_TIME.value)
        if republish_after:
            republish_after = str_to_datetime(
                timestamp=republish_after, date_format="%Y-%m-%d"
            )

        republish_till = record.get(APIParameters.RE_PUBLISH_TILL_TIME.value)
        if republish_till:
            republish_till = str_to_datetime(
                timestamp=republish_till, date_format="%Y-%m-%d"
            )

        parser = GetCompanyNewsFeed(
            CREDENTIALS[NEWS_FEEDS["DATABASE"]], service_id
        )

        parser.execute_news_feed_parser(
            company_cin=company_cin,
            feed_source=feed_source,
            start_date=republish_after,
            end_date=republish_till,
        )

        logger.info(f"Successfully ran for index record={index}")

    logger.info("Finished the job successfully")
