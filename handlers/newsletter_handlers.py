import json
import sys

sys.path.append("/opt")
from service.newsletter_service import NewsletterService

from utils.decorators import non_api_exception_handler
from utils.logger import get_logger

logger = get_logger(__name__)


@non_api_exception_handler(logger=logger)
def run_newsletter_handler(event, context):
    logger.debug(
        f"Running the newsletter for the event={event} and context={context}"
    )

    file_name = event.get("input_event_file_name")
    if not file_name:
        logger.info(f"Loading the input data from the filename={file_name} ")
        return ["Input filename missing"]

    with open(file_name, "r", encoding="utf-8") as file:
        json_config = json.load(file)

        newsletter_configs = json_config.get("newsletter_configs")

        if not newsletter_configs or len(newsletter_configs) == 0:
            logger.info("No newsletter config found, so stopping the process")
            return []

        NewsletterService().run_newsletter(
            newsletter_configs=newsletter_configs
        )

        logger.info("Successfully ran the job for the newsletter")
        return []
