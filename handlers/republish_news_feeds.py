import sys


sys.path.append("/opt")
import json
import traceback
from datetime import datetime
from utils.logger import get_logger
from utils.exceptions.exceptions import CustomException
from config import NEWS_FEEDS, CREDENTIALS
from constants.api_parameters import APIParameters
from service.ds_rss_feed_parser.get_company_news_feed import GetCompanyNewsFeed

logger = get_logger(__name__)


def republish_news_feed_handler(event, context):
    logger.info(
        f"Starting Lambda run to republish news feed for context={context}"
    )
    # """
    # Lambda to trigger a republish of feeds. Expected input is
    # {
    #     'service_id':'',
    #     'entity_id_list':['optional'],
    #     'republish_after': 'YYYY-MM-DD HH:MM:SS',
    #     'republish_till': 'YYYY-MM-DD HH:MM:SS',
    #     'only_denoised': [true/false],
    #     'max_articles': #of articles to process
    # }
    #
    # """
    try:
        status_code = 200
        message = "Completed"

        start_time = event[APIParameters.RE_PUBLISH_AFTER_TIME.value]
        start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")

        end_time = event.get(APIParameters.RE_PUBLISH_TILL_TIME.value, None)
        end_time = (
            datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
            if end_time
            else None
        )

        max_articles_to_republish = event.get(
            APIParameters.MAX_ARTICLES.value, 50000
        )
        only_denoised = event.get(APIParameters.ONLY_DENOISED.value, True)

        parser = GetCompanyNewsFeed(
            CREDENTIALS[NEWS_FEEDS["DATABASE"]],
            event[APIParameters.AUDIT_UUID_FIELD.value],
        )
        parser.republish_feeds(
            entity_id_list=event.get(APIParameters.ENTITY_ID_LIST.value, []),
            start_time=start_time,
            end_time=end_time,
            max_articles_to_republish=max_articles_to_republish,
            only_denoised=only_denoised,
        )
    except CustomException as error:
        status_code = 500
        message = f"Feed republish failed with error {repr(error)}"
        traceback.print_exc()
        logger.error(message)

    return {"statusCode": status_code, "body": json.dumps(message)}
