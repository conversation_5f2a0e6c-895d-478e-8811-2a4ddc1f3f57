import sys


sys.path.append("/opt")
import traceback
import json

from config import CREDENTIALS, NEWS_FEEDS
from utils.logger import get_logger
from utils.exceptions.exceptions import CustomException

from service.ds_news_feeds_invoker import NewsCompanyInvoker

logger = get_logger(__name__)


def news_feed_invoker_handler(event, context):
    """
    sample event
    {
        "companies": [
            {
                "cin": "U19119PN2020PTC190137",
                "name_for_news": "Aretto",
                "name": "<PERSON><PERSON>"
            }
        ]
    }
    """
    logger.info(f"Starting Lambda run for context={context}")
    try:
        if event is None:
            logger.info(f"Event={event} is empty")
            raise CustomException("Empty event.")

        companies = event.get("companies")
        status_code = 200
        message = "Completed"

        invoker = NewsCompanyInvoker(CREDENTIALS[NEWS_FEEDS["DATABASE"]])
        invoker.execute_invoker(companies)
    except CustomException as error:
        status_code = 500
        message = f"News feed invoker failed with error {repr(error)}"
        traceback.print_exc()
        logger.error(f"Processing news failed with error {repr(error)}")
        return {"statusCode": status_code, "body": json.dumps(message)}

    return {"statusCode": status_code, "body": json.dumps(message)}
