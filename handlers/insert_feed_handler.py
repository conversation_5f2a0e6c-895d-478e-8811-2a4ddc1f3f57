import sys


sys.path.append("/opt")
import traceback
import json

from config import CREDENTIALS, NEWS_FEEDS
from service.insert_feed_service import InsertNewsFeed
from utils.logger import get_logger
from utils.exceptions.exceptions import CustomException


logger = get_logger(__name__)
insert_news_feed = InsertNewsFeed(CREDENTIALS[NEWS_FEEDS["DATABASE"]])


def insert_feed_handler(event, context):
    """
    event = {
        "Records": [
            {
                "eventSource": "aws:sqs",
                "body": json.dumps({
                    'entity_type': 'company/sector',
                    'cin': 'cin',
                    'name': 'company',
                    'raw_link': 'www.google.com',
                    'news_link': 'www.google.com',
                    'image_link': 'www.google.com',
                    'published_date': '2020-11-10 00:00:00',
                    'source': 'Google',
                    'raw_title': 'sample title',
                    'parser_service': 'internal-news',
                    'service_id': 'uuid',
                    'description': 'sample description',
                    'ignore_publishing': False
                })
            }
        ]
    }
    """
    try:
        if not event:
            logger.info(
                f"Empty event = {event} and context={context} received"
            )

        logger.debug(f"Processing event = {event}")

        records = event["Records"]
        if not records:
            logger.info(f"Empty Records = {records} received")
        if records[0].get("eventSource") == "aws:sqs":
            logger.info("Event received from SQS")
            all_news_links = []
            for record in records:
                message_data = json.loads(record["body"])
                all_news_links.append(message_data)
            logger.info(f"Total records found = {len(all_news_links)}")
            insert_news_feed.process_urls(all_news_links)

    except CustomException as ex:
        traceback.print_exc()
        message = f"Sending raw messages to the denoiser service failed due to {repr(ex)}"
        logger.error(message)

    return "Success"
