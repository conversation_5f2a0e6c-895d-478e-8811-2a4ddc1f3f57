# rss-feed-parser

Feed parser service for fetching different rss-feeds

## Services
* google_news_feed_invoker_handler - <PERSON><PERSON> to identify all companies for which we need to fetch news and invoke the lambda to fetch news for each of these companies
* get_company_google_feed_handler - <PERSON><PERSON> to fetch all news links for the given company CIN since the last time the job ran using google news
* rss-feeds-parser - <PERSON><PERSON> to invoke all the independent rss news feeds. It stores the URLs into mongoDB and publishes the links to be denoiser
* news-feed-parser - Invokes and fetches the news for blokAM dashboard
## Structure
The code base is organized as follows
* common - Contains generic code for logging and different util functions
* handlers - Has all the Lambda handlers for the different services
* service - Has the service layer containing news-feed parsers and ds-rss-feed parsers
* constants - contains the list of string
* test - contains the test file for the different feed parsers


## Requirements

| Language     | Download                          |
| ------------ | --------------------------------- |
| Python 3.8   | https://www.python.org/downloads/ |

## Set up

* Install requirements
```sh
pip3 install -r requirements.txt
```
* Update config.py to point to your local DB
* Get DB schema from token-harvester

