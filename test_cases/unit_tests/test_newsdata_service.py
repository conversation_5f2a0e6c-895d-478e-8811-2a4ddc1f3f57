import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
import requests
import json

from service.news_sources.newsdata_service import NewsdataService
from utils.exceptions.exceptions import NewsdataAPIException


class TestNewsdataServiceRetrieveFeed(unittest.TestCase):
    """
    Unit tests for the retrieve_feed method in NewsdataService.
    
    This test suite verifies the functionality of the retrieve_feed method
    including successful API calls, error handling, and edge cases.
    """

    def setUp(self):
        """Set up test fixtures before each test method."""
        # Mock config values to avoid dependency on actual config
        self.mock_config_patcher = patch('service.news_sources.newsdata_service.config')
        self.mock_config = self.mock_config_patcher.start()
        
        # Mock config values
        self.mock_config.CREDENTIALS = {"test_db": {"connection": "test"}}
        self.mock_config.NEWS_FEEDS = {"DATABASE": "test_db"}
        self.mock_config.NEWSDATA_TOKENS = {"token1": "test_token_123"}
        self.mock_config.NEWSDATA_ENDPOINT = "https://newsdata.io/api/1/news"
        
        # Mock BaseParser to avoid database dependencies
        with patch('service.news_sources.newsdata_service.BaseParser.__init__', return_value=None):
            self.service = NewsdataService(uuid="test-service-id")
        
        # Set up test data
        self.test_from_date = datetime(2023, 1, 1)
        self.test_till_date = datetime(2023, 1, 31)
        self.test_company_name = "Test Company"
        
        # Mock successful API response
        self.successful_response_data = {
            "status": "success",
            "totalResults": 2,
            "results": [
                {
                    "article_id": "test_id_1",
                    "title": "Test Article 1",
                    "link": "https://example.com/article1",
                    "description": "Test description 1",
                    "content": "Test content 1",
                    "pubDate": "2023-01-15 10:00:00",
                    "image_url": "https://example.com/image1.jpg",
                    "source_name": "Test Source",
                    "language": "english",
                    "country": ["united states"],
                    "category": ["business"]
                },
                {
                    "article_id": "test_id_2",
                    "title": "Test Article 2",
                    "link": "https://example.com/article2",
                    "description": "Test description 2",
                    "content": "Test content 2",
                    "pubDate": "2023-01-20 15:30:00",
                    "image_url": "https://example.com/image2.jpg",
                    "source_name": "Another Source",
                    "language": "english",
                    "country": ["united kingdom"],
                    "category": ["technology"]
                }
            ]
        }

    def tearDown(self):
        """Clean up after each test method."""
        self.mock_config_patcher.stop()

    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_success(self, mock_get):
        """Test successful API call and response parsing."""
        # Arrange
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = self.successful_response_data
        mock_get.return_value = mock_response

        # Act
        result = self.service.retrieve_feed(
            news_from_date=self.test_from_date,
            news_till_date=self.test_till_date,
            company_name=self.test_company_name
        )

        # Assert
        self.assertEqual(result, self.successful_response_data)
        mock_get.assert_called_once()
        
        # Verify the URL construction
        call_args = mock_get.call_args
        self.assertIn("https://newsdata.io/api/1/news", call_args[0][0])
        self.assertIn("qInTitle=Test+Company", call_args[0][0])
        self.assertIn("language=en", call_args[0][0])

    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_api_failure_status(self, mock_get):
        """Test handling of API response with failure status."""
        # Arrange
        failure_response = {
            "status": "error",
            "results": {
                "message": "API key is invalid",
                "code": "InvalidApiKey"
            }
        }
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = failure_response
        mock_get.return_value = mock_response

        # Act & Assert
        with self.assertRaises(NewsdataAPIException):
            self.service.retrieve_feed(
                news_from_date=self.test_from_date,
                news_till_date=self.test_till_date,
                company_name=self.test_company_name
            )


    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_timeout_exception(self, mock_get):
        """Test handling of requests.Timeout exception."""
        # Arrange
        mock_get.side_effect = requests.Timeout("Request timed out")

        # Act & Assert
        with self.assertRaises(NewsdataAPIException) as context:
            self.service.retrieve_feed(
                news_from_date=self.test_from_date,
                news_till_date=self.test_till_date,
                company_name=self.test_company_name
            )
        self.assertIn('{}', str(context.exception))


    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_empty_response(self, mock_get):
        """Test handling of empty or None response data."""
        # Arrange
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = None
        mock_get.return_value = mock_response

        # Act
        result = self.service.retrieve_feed(
            news_from_date=self.test_from_date,
            news_till_date=self.test_till_date,
            company_name=self.test_company_name
        )

        # Assert
        self.assertIsNone(result)

    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_empty_dict_response(self, mock_get):
        """Test handling of empty dictionary response."""
        # Arrange
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {}
        mock_get.return_value = mock_response

        # Act
        result = self.service.retrieve_feed(
            news_from_date=self.test_from_date,
            news_till_date=self.test_till_date,
            company_name=self.test_company_name
        )

        # Assert
        self.assertIsNone(result)

    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_url_encoding(self, mock_get):
        """Test proper URL encoding of parameters."""
        # Arrange
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = self.successful_response_data
        mock_get.return_value = mock_response
        
        company_with_spaces = "Test Company & Co."

        # Act
        self.service.retrieve_feed(
            news_from_date=self.test_from_date,
            news_till_date=self.test_till_date,
            company_name=company_with_spaces
        )

        # Assert
        call_args = mock_get.call_args
        url = call_args[0][0]
        # Check that spaces and special characters are properly encoded
        self.assertIn("Test+Company+%26+Co.", url)

    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_parameters_construction(self, mock_get):
        """Test that API parameters are correctly constructed."""
        # Arrange
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = self.successful_response_data
        mock_get.return_value = mock_response

        # Act
        self.service.retrieve_feed(
            news_from_date=self.test_from_date,
            news_till_date=self.test_till_date,
            company_name=self.test_company_name
        )

        # Assert
        call_args = mock_get.call_args
        url = call_args[0][0]
        
        # Verify all required parameters are present
        self.assertIn("apikey=test_token_123", url)
        self.assertIn("qInTitle=Test+Company", url)
        self.assertIn("from_date=2023-01-01", url)
        self.assertIn("to_date=2023-01-31", url)
        self.assertIn("language=en", url)


    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_retry_decorator_behavior(self, mock_get):
        """Test that the retry decorator is properly applied."""
        # Arrange
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = self.successful_response_data
        mock_get.return_value = mock_response

        # Act
        result = self.service.retrieve_feed(
            news_from_date=self.test_from_date,
            news_till_date=self.test_till_date,
            company_name=self.test_company_name
        )

        # Assert
        self.assertEqual(result, self.successful_response_data)
        # Verify that the method was called (retry decorator should not interfere with normal execution)
        mock_get.assert_called_once()

    def test_retrieve_feed_date_parameter_types(self):
        """Test that the method accepts datetime objects as parameters."""
        # This test verifies the method signature accepts datetime objects
        # without actually making an API call

        with patch('service.news_sources.newsdata_service.requests.get') as mock_get:
            mock_response = Mock()
            mock_response.raise_for_status.return_value = None
            mock_response.json.return_value = self.successful_response_data
            mock_get.return_value = mock_response

            # Should not raise any type errors
            result = self.service.retrieve_feed(
                news_from_date=datetime(2023, 1, 1, 10, 30, 45),
                news_till_date=datetime(2023, 1, 31, 23, 59, 59),
                company_name="Test Company"
            )

            self.assertEqual(result, self.successful_response_data)

    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_special_characters_in_company_name(self, mock_get):
        """Test handling of special characters in company name."""
        # Arrange
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = self.successful_response_data
        mock_get.return_value = mock_response

        special_company_names = [
            "Company & Associates",
            "Company-Name Inc.",
            "Company (Holdings) Ltd.",
            "Company's Business",
            "<EMAIL>",
            "Company+Plus"
        ]

        for company_name in special_company_names:
            with self.subTest(company_name=company_name):
                # Act
                result = self.service.retrieve_feed(
                    news_from_date=self.test_from_date,
                    news_till_date=self.test_till_date,
                    company_name=company_name
                )

                # Assert
                self.assertEqual(result, self.successful_response_data)

    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_response_with_missing_status(self, mock_get):
        """Test handling of response without status field."""
        # Arrange
        response_without_status = {
            "totalResults": 0,
            "results": []
        }
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = response_without_status
        mock_get.return_value = mock_response

        # Act & Assert
        with self.assertRaises(NewsdataAPIException):
            self.service.retrieve_feed(
                news_from_date=self.test_from_date,
                news_till_date=self.test_till_date,
                company_name=self.test_company_name
            )

    @patch('service.news_sources.newsdata_service.requests.get')
    def test_retrieve_feed_successful_with_no_results(self, mock_get):
        """Test successful API call but with no results."""
        # Arrange
        empty_success_response = {
            "status": "success",
            "totalResults": 0,
            "results": []
        }
        mock_response = Mock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = empty_success_response
        mock_get.return_value = mock_response

        # Act
        result = self.service.retrieve_feed(
            news_from_date=self.test_from_date,
            news_till_date=self.test_till_date,
            company_name=self.test_company_name
        )

        # Assert
        self.assertEqual(result, empty_success_response)
        self.assertEqual(result["totalResults"], 0)
        self.assertEqual(len(result["results"]), 0)



