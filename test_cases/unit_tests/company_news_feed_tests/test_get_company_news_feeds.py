import json
import unittest
import uuid
from datetime import datetime
from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

from dateutil import parser

import config
from constants.api_parameters import APIParameters
from constants.entities import FeedSources
from constants.entities import GoogleNewsEntities
from handlers.get_company_news_feeds_handler import (
    get_company_news_feeds_handler,
)
from infrastructure.repository.base_repository import BaseRepository
from service.ds_rss_feed_parser.get_company_news_feed import GetCompanyNewsFeed
from service.get_company_bing_services import BingServices


class TestGetCompanyNewsFeeds(unittest.TestCase, BaseRepository):
    """Unit tests for handling company news feeds from Google News and Bing News sources."""

    def setUp(self):
        BaseRepository.__init__(self, config.DEFAULT_TIMEOUT_MS)
        # self.maxDiff = None
        self._sample_cin1 = str(uuid.uuid4().hex)
        self._sample_cin2 = str(uuid.uuid4().hex)
        self._sample_cin3 = str(uuid.uuid4().hex)

        self.feed_parser_job_status_doc = {
            "cin": self._sample_cin1,
            "parser_service": FeedSources.GOOGLE_NEWS.value,
            "date_of_inc": parser.isoparse("2018-02-04T00:00:00.000+0000"),
            "last_run_end_date": parser.isoparse(
                "2024-01-12T21:16:56.287+0000"
            ),
            "last_run_start_date": parser.isoparse(
                "2023-07-12T21:16:56.287+0000"
            ),
            "news_from_date": parser.isoparse("2017-01-12T21:16:56.287+0000"),
            "news_start_date": datetime.now() - timedelta(days=40),
            "news_till_date": datetime.now(),
        }
        self.company_info_doc1 = {
            "cin": self._sample_cin1,
            "company": {
                "description": "Bellatrix Aerospace is an Indian space "
                "company at the forefront of cutting-edge "
                "technologies"
            },
            "name": "Bellatrix",
            "name_for_news": ["company_news_name"],
        }
        self.company_info_doc = {
            "cin": self._sample_cin2,
            "company": {
                "description": "Bellatrix Aerospace is an Indian space "
                "company at the forefront of cutting-edge "
                "technologies"
            },
            "name": "Bellatrix",
            "name_for_news": ["company_news_name"],
        }
        self.company_info_doc2 = {
            "cin": self._sample_cin3,
            "company": {
                "description": "Bellatrix Aerospace is an Indian space "
                "company at the forefront of cutting-edge "
                "technologies"
            },
            "name": "Bellatrix",
            "name_for_news": ["company_news_name"],
        }
        self.company_info_doc3 = {
            "cin": "CIN123456",
            "name": "non description company",
        }
        self.bing_feed_parser_job_status_doc = {
            "cin": self._sample_cin3,
            "parser_service": FeedSources.BING_NEWS.value,
            "date_of_inc": parser.isoparse("2018-02-04T00:00:00.000+0000"),
            "last_run_end_date": parser.isoparse(
                "2024-01-12T21:16:56.287+0000"
            ),
            "last_run_start_date": parser.isoparse(
                "2023-07-12T21:16:56.287+0000"
            ),
            "news_from_date": parser.isoparse("2017-01-12T21:16:56.287+0000"),
            "news_start_date": datetime.now() - timedelta(days=40),
            "news_till_date": datetime.now(),
        }
        self.feed_parser_job_status_collection = (
            APIParameters.FEED_PARSER_JOB_STATUS_COLLECTION.value
        )
        self.company_info_collection = (
            APIParameters.COMPANY_INFO_COLLECTION.value
        )
        self.insert(
            collection_name=self.feed_parser_job_status_collection,
            document=[
                self.feed_parser_job_status_doc,
                self.bing_feed_parser_job_status_doc,
            ],
        )
        self.insert(
            collection_name=self.company_info_collection,
            document=[
                self.company_info_doc,
                self.company_info_doc1,
                self.company_info_doc2,
                self.company_info_doc3,
            ],
        )
        boto3_client_patcher = patch("service.audit_service.boto3.client")
        self.mock_boto3 = boto3_client_patcher.start()
        self.mock_boto3.send_message.return_value = {}

    @patch("service.ds_rss_feed_parser.get_company_news_feed.fetch_parameter")
    def test_company_description_exist(self, mock_fetch):
        mock_fetch.return_value = (
            '{"google_flagged": false, '
            '"google_flagged_timestap": "2024-11-26 00:00:00"}'
        )
        feed_source = FeedSources.GOOGLE_NEWS.value
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        {
                            "service_id": "service_id",
                            "name_for_news": ["company_news_name"],
                            "name": "Bellatrix",
                            "cin": self._sample_cin2,
                            "feed_source": feed_source,
                        }
                    )
                }
            ]
        }

        with self.assertLogs(
            "service.ds_rss_feed_parser.get_company_news_feed", level="INFO"
        ) as log:
            get_company_news_feeds_handler(event, context=None)
        # self.assertFalse(result)
        self.assertIn(
            f"INFO:service.ds_rss_feed_parser."
            f"get_company_news_feed:started parser of {feed_source}",
            log.output,
        )

    @patch("utils.aws_system_manager_utils.parameter_store_utils.ssm_client")
    def test_company_description_not_exist_check(self, mock_ssm_client):
        company_cin = "CIN123456"
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "google_flagged_timestamp": "2025-01-30 00:00:00",
                        "google_flagged": False,
                    }
                )
            }
        }
        mock_ssm_client.put_parameter.return_value = 1
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        {
                            "service_id": "service_id",
                            "name_for_news": ["company_news_name"],
                            "name": "Bellatrix",
                            "cin": company_cin,
                            "feed_source": "Google News",
                        }
                    )
                }
            ]
        }
        with self.assertLogs(
            "service.ds_rss_feed_parser.get_company_news_feed", level="ERROR"
        ) as log:
            get_company_news_feeds_handler(event, context=None)
        self.assertIn(
            f"ERROR:service.ds_rss_feed_parser."
            f"get_company_news_feed:company description does not exist for "
            f"cin:{company_cin}",
            log.output,
        )

    # TODO: Write better test cases without asserting logs
    # @patch("service.ds_rss_feed_parser.get_company_news_feed.fetch_parameter")
    # @patch.object(
    #     GetCompanyNewsFeed, "company_description_exist", return_value=True
    # )
    # def test_limiting_google_news_for_fetching_latest_news(
    #     self, mocked_rsp, mock_fetch
    # ):
    #     feed_source = config.FeedSources.GOOGLE_NEWS.value
    #     mock_fetch.return_value = (
    #         '{"google_flagged": true, '
    #         '"google_flagged_timestamp": "2024-11-26 00:00:00"}'
    #     )
    #     mocked_rsp.return_value = True
    #     event = {
    #         "Records": [
    #             {
    #                 "body": json.dumps(
    #                     {
    #                         "service_id": "service_id",
    #                         "name_for_news": ["company_news_name"],
    #                         "name": "Beta INC",
    #                         "cin": self._sample_cin1,
    #                         "feed_source": feed_source,
    #                     }
    #                 )
    #             }
    #         ]
    #     }
    #     with self.assertLogs("utils.news_feed_utils", level="INFO") as log:
    #         get_company_news_feeds_handler(event, context=None)
    #     # self.assertFalse(result)
    #     self.assertIn(
    #         "INFO:utils.news_feed_utils:We have extracted all the "
    #         "past news.",
    #         log.output,
    #     )
    #

    @patch("service.ds_rss_feed_parser.get_company_news_feed.fetch_parameter")
    def test_google_news_flag_true(self, mock_fetch):
        current_date_with_time = datetime.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        )
        current_date_str = current_date_with_time.strftime("%Y-%m-%d %H:%M:%S")
        mock_fetch.return_value = (
            f'{{"google_flagged": true, '
            f'"google_flagged_timestap": "{current_date_str}"}}'
        )
        feed_source = FeedSources.GOOGLE_NEWS.value
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        {
                            "service_id": "service_id",
                            "name_for_news": ["company_news_name"],
                            "name": "Beta INC",
                            "cin": self._sample_cin1,
                            "feed_source": feed_source,
                        }
                    )
                }
            ]
        }
        with self.assertLogs(
            "service.ds_rss_feed_parser.get_company_news_feed", level="INFO"
        ) as log:
            # result = get_company_news_feeds_handler(event, context=None)
            get_company_news_feeds_handler(event, context=None)
        # self.assertFalse(result)
        scheduled_datetime = datetime.now().replace(
            hour=0, minute=0, second=0, microsecond=0
        ) + timedelta(
            hours=GoogleNewsEntities.GOOGLE_NEWS_REFRESH_HOUR_LIMIT.value
        )
        self.assertIn(
            f"INFO:service.ds_rss_feed_parser.get_company_news_feed:"
            f"Google news limit is exhausted please try at :{scheduled_datetime}",
            log.output,
        )

    @patch("service.ds_rss_feed_parser.get_company_news_feed.fetch_parameter")
    @patch("service.ds_rss_feed_parser.get_company_news_feed.put_parameter")
    def test_google_reset_flag(self, mock_put, mock_fetch):
        mock_fetch.return_value = json.dumps(
            {
                "google_flagged": True,
                "google_flagged_timestap": "2024-11-25 00:00:00",
            }
        )
        mock_put.return_value = True
        feed_source = FeedSources.GOOGLE_NEWS.value
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        {
                            "service_id": "service_id",
                            "name_for_news": ["company_news_name"],
                            "name": "Beta INC",
                            "cin": self._sample_cin1,
                            "feed_source": feed_source,
                        }
                    )
                }
            ]
        }
        with self.assertLogs(
            "service.ds_rss_feed_parser.get_company_news_feed", level="INFO"
        ) as log:
            # result = get_company_news_feeds_handler(event, context=None)
            get_company_news_feeds_handler(event, context=None)
        # self.assertFalse(result)
        self.assertIn(
            "INFO:service.ds_rss_feed_parser.get_company_news_feed:"
            "google news parser details:{'google_flagged_timestap': '2024-11-25 00:00:00', "
            "'google_flagged': False} to be updated",
            log.output,
        )

    @patch.object(GetCompanyNewsFeed, "_send_to_audit_logs")
    @patch.object(BingServices, "bing_worker")
    def test_bing_news_date_handling_new_company(
        self, mock_retrieve_articles, mock_audit
    ):
        feed_source = FeedSources.BING_NEWS.value
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        {
                            "service_id": "service_id",
                            "name_for_news": ["company_news_name"],
                            "name": "Bellatrix",
                            "cin": self._sample_cin2,
                            "feed_source": feed_source,
                        }
                    )
                }
            ]
        }
        mock_audit.return_value = True
        expected_log_pattern = (
            "INFO:service.ds_rss_feed_parser.get_company_news_feed:"
            "process bing feed for the feed_source=Bing News"
        )
        mock_retrieve_articles.return_value = []
        query = {"cin": self._sample_cin2}
        with self.assertLogs(
            "service.ds_rss_feed_parser.get_company_news_feed", level="INFO"
        ) as log:
            get_company_news_feeds_handler(event, context=None)
        projection = {"news_from_date": 1, "news_till_date": 1, "_id": 0}
        res = self.find_one(
            query,
            projection=projection,
            collection_name=self.feed_parser_job_status_collection,
        )
        job_parser_news_till_date = res.get("news_till_date")
        job_parser_from_date = res.get("news_from_date")
        self.assertEqual(
            job_parser_news_till_date.date(),
            (datetime.now() - timedelta(days=3)).date(),
        )
        self.assertEqual(job_parser_from_date, datetime(2018, 1, 1, 0, 0))
        self.assertTrue(
            any(expected_log_pattern in log_line for log_line in log.output)
        )

    @patch.object(GetCompanyNewsFeed, "_send_to_audit_logs")
    @patch.object(BingServices, "bing_worker")
    def test_bing_news_date_handling_existing_company(
        self, mock_retrieve_articles, mock_audit
    ):
        feed_source = FeedSources.BING_NEWS.value
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        {
                            "service_id": "service_id",
                            "name_for_news": ["company_news_name"],
                            "name": "Swiggy",
                            "cin": self._sample_cin3,
                            "feed_source": feed_source,
                        }
                    )
                }
            ]
        }
        mock_audit.return_value = True
        expected_log_pattern = (
            "INFO:utils.news_feed_utils:"
            "Setting start and end date for existing company towards forward approach"
        )
        mock_retrieve_articles.return_value = []
        query = {"cin": self._sample_cin3}
        with self.assertLogs("utils.news_feed_utils", level="INFO") as log:
            get_company_news_feeds_handler(event, context=None)
        projection = {"news_from_date": 1, "news_till_date": 1, "_id": 0}
        res = self.find_one(
            query,
            projection=projection,
            collection_name=self.feed_parser_job_status_collection,
        )
        job_parser_news_till_date = res.get("news_till_date")
        job_parser_from_date = res.get("news_from_date")
        self.assertEqual(
            job_parser_news_till_date.date(), datetime.now().date()
        )
        self.assertEqual(job_parser_from_date.date(), datetime.now().date())
        self.assertTrue(
            any(expected_log_pattern in log_line for log_line in log.output)
        )

    def tearDown(self):
        self.delete(
            collection_name=self.feed_parser_job_status_collection,
            query={"cin": self._sample_cin1},
        )
        self.delete(
            collection_name=self.company_info_collection,
            query={"cin": self._sample_cin2},
        )
