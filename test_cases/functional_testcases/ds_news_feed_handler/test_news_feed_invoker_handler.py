import copy
import unittest
from datetime import datetime
from datetime import timedelta

import config
from constants.api_parameters import APIParameters
from handlers.ds_news_feed_handler import news_feed_invoker_handler
from infrastructure.repository.base_repository import BaseRepository
from test_cases.helpers.input_data import COMPANY_INFO
from test_cases.helpers.input_data import db_entry_for_news_test
from test_cases.helpers.input_data import feed_parser_job_entry_for_news_test
from utils.exceptions.exceptions import CustomException


class TestNewsFeedInvokerHandler(unittest.TestCase):
    """
    Unit tests for the methods in the News Feed Invoker Handler.

    This testcase suite verifies the functionality of methods within the news_feed_invoker_handler.

    """

    def setUp(self):
        self._base_repo = BaseRepository(config.DEFAULT_TIMEOUT_MS)
        info_data = copy.deepcopy(COMPANY_INFO)
        self._base_repo.insert(
            collection_name=APIParameters.COMPANY_INFO_COLLECTION.value,
            document=info_data,
        )

    def tearDown(self):
        self._base_repo.delete(
            collection_name=APIParameters.COMPANY_INFO_COLLECTION.value,
            query={},
        )

    def test_empty_event(self):
        event = None
        context = None
        news_feed_invoker_handler(event=event, context=context)
        self.assertRaises(CustomException)

    def test_no_default_companies(self):
        event = {"companies": []}
        context = None
        self._base_repo.insert(
            collection_name=APIParameters.COMPANY_INFO_COLLECTION.value,
            document=db_entry_for_news_test,
        )
        response = news_feed_invoker_handler(event=event, context=context)
        self.assertEqual(response["body"], '"Completed"')

    def test_with_default_companies(self):
        event = {
            "companies": [
                {
                    "cin": "U19119PN2020PTC190137",
                    "company": {"name_for_news": "Arettonamefornews"},
                    "name": "Aretto",
                    "news_till_date": datetime.now() - timedelta(days=4),
                }
            ]
        }
        context = None
        response = news_feed_invoker_handler(event=event, context=context)
        self.assertEqual(response["body"], '"Completed"')

    def test_to_ignore_recent_companies(self):
        event = {"companies": []}
        self._base_repo.insert(
            collection_name=APIParameters.COMPANY_INFO_COLLECTION.value,
            document=db_entry_for_news_test,
        )
        self._base_repo.insert(
            collection_name=APIParameters.FEED_PARSER_JOB_STATUS_COLLECTION.value,
            document=feed_parser_job_entry_for_news_test,
        )
        context = None
        response = news_feed_invoker_handler(event=event, context=context)
        self.assertEqual(response["body"], '"Completed"')
