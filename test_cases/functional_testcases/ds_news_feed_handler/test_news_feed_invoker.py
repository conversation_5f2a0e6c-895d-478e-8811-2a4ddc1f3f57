import unittest
from unittest.mock import patch

import config
from constants.api_parameters import APIParameters
from handlers.ds_news_feed_handler import news_feed_invoker_handler
from infrastructure.repository.base_repository import BaseRepository
from test_cases.helpers.input_helper import company_info_doc_1
from test_cases.helpers.input_helper import company_info_doc_2
from test_cases.helpers.input_helper import company_info_doc_3
from test_cases.helpers.input_helper import company_info_doc_4
from test_cases.helpers.input_helper import company_info_doc_5
from test_cases.helpers.input_helper import feed_job_parser_cin1
from test_cases.helpers.input_helper import feed_job_parser_cin2
from test_cases.helpers.input_helper import feed_job_parser_cin3
from utils.sqs import SqsService


class TestNewsFeedInvoker(unittest.TestCase, BaseRepository):
    """
    Unit tests for validating the news feed invoker's functionality and SQS message handling.
    """

    def setUp(self):
        BaseRepository.__init__(self, config.DEFAULT_TIMEOUT_MS)
        self.company_info_collection = (
            APIParameters.COMPANY_INFO_COLLECTION.value
        )
        self.feed_parser_job_status_collection = (
            APIParameters.FEED_PARSER_JOB_STATUS_COLLECTION.value
        )

    @patch.object(SqsService, "send_message_to_queue")
    def test_empty_event(self, mock_sqs_queue):
        event = {}
        context = None
        mock_sqs_queue.return_value = True
        self.insert(
            collection_name=self.company_info_collection,
            document=[company_info_doc_1],
        )
        self.insert(
            collection_name=self.feed_parser_job_status_collection,
            document=[feed_job_parser_cin1],
        )
        res = news_feed_invoker_handler(event, context)
        self.tearDown()
        self.assertEqual(res["statusCode"], 200)

    @patch.object(SqsService, "send_message_to_queue")
    def test_recent_news_till_date(self, mock_sqs_queue):
        event = {}
        context = None
        mock_sqs_queue.return_value = True
        self.insert(
            collection_name=self.company_info_collection,
            document=[
                company_info_doc_1,
                company_info_doc_2,
                company_info_doc_3,
                company_info_doc_4,
                company_info_doc_5,
            ],
        )
        self.insert(
            collection_name=self.feed_parser_job_status_collection,
            document=[
                feed_job_parser_cin1,
                feed_job_parser_cin3,
                feed_job_parser_cin2,
            ],
        )
        with self.assertLogs(
            "service.ds_news_feeds_invoker", level="INFO"
        ) as log:
            news_feed_invoker_handler(event, context)
        expected_log = "INFO:service.ds_news_feeds_invoker:Total active companies in the system =4"
        self.tearDown()
        self.assertTrue(any(expected_log in log for log in log.output))

    @patch.object(SqsService, "send_message_to_queue")
    def test_new_company(self, mock_sqs_queue):
        self.insert(
            collection_name=self.company_info_collection,
            document=[company_info_doc_4],
        )
        mock_sqs_queue.return_value = True
        with self.assertLogs(
            "service.ds_news_feeds_invoker", level="INFO"
        ) as log:
            news_feed_invoker_handler(event={}, context=None)
        expected_log = "Triggering the news job for 1 companies"
        self.assertTrue(
            any(expected_log in log_line for log_line in log.output)
        )

    def tearDown(self):
        self.delete(
            collection_name=self.feed_parser_job_status_collection,
            query={},
        )
        self.delete(
            collection_name=self.company_info_collection,
            query={},
        )
