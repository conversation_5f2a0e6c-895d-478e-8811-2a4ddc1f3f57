import json
import os
import time
import unittest
from datetime import datetime
from unittest.mock import MagicMock
from unittest.mock import patch

from handlers.newsletter_handlers import run_newsletter_handler
from service.base_parser import BaseParser


class TestRunNewsletterHandler(unittest.TestCase):
    """
    Testcases for the handler run_newsletter_handler
    """

    def setUp(self):
        self.testing_file = "test.json"
        self.test_no_config_file = "test_no_config.json"
        dummy_data = {
            "newsletter_configs": [
                {
                    "newsletter_source": "gmail",
                    "newsletter_email": "email id",
                    "newsletter_password": "password",
                    "newsletter_name": "arc",
                    "newsletter_search_criteria": {
                        "from_address": "",
                        "subject": "Google Alerts",
                    },
                    "newsletter_date_format": "%a, %d %b %Y %H:%M:%S %z",
                }
            ]
        }
        with open(self.testing_file, "w", encoding="utf-8") as f:
            json.dump(dummy_data, f)

    def tearDown(self):
        retries = 3
        while retries > 0:
            try:
                if os.path.exists(self.testing_file):
                    os.remove(self.testing_file)
                if os.path.exists(self.test_no_config_file):
                    os.remove(self.test_no_config_file)
                break
            except PermissionError:
                retries -= 1
                time.sleep(0.1)

    def test_no_filename(self):
        event = {}
        res = run_newsletter_handler(event, None)
        self.assertEqual(res, ["Input filename missing"])

    def test_no_config(self):
        dummy_data_no_config = {"news_alert_configs": []}
        with open(self.test_no_config_file, "w", encoding="utf-8") as f:
            json.dump(dummy_data_no_config, f)
        event = {
            "input_event_file_name": "test_no_config.json",
        }
        res = run_newsletter_handler(event, None)
        self.assertEqual(res, [])

    @patch("utils.general_utils.requests.post")
    @patch("service.newsletter_service.extract_arc_newsletter")
    @patch("service.mail.gmail_service.imaplib.IMAP4_SSL")
    @patch.object(BaseParser, "get")
    def test_success(
        self,
        mock_get,
        mock_imap,
        mock_extract_arc_newsletter,
        mock_post_requests,
    ):
        get_companies_resp = [
            {
                "cin": "sample",
                "name": "sample",
                "is_active": True,
                "is_ready": True,
            }
        ]

        def side_effect_generator():
            yield {"last_run_end_date": datetime.now()}
            i = 1
            while i < 20:
                i += 1
                yield get_companies_resp
            yield None

        mock_get.side_effect = side_effect_generator()
        mock_imap = mock_imap.return_value
        mock_imap.login.return_value = None
        mock_imap.search.return_value = "OK", [b"1 2 3 4 5"]
        mock_imap.fetch.return_value = "OK", [
            (
                b"1 (RFC822 {342})",
                b"From: <EMAIL>\r\n"
                b"To: <EMAIL>\r\n"
                b"Subject: Google Alerts - Test Subject\r\n"
                b"Date: Wed, 23 Apr 2025 10:00:00 +0000\r\n"
                b'Content-Type: text/plain; charset="UTF-8"\r\n'
                b"\r\n"
                b"This is a test email body from Google Alerts.\r\n",
            )
        ]
        mock_extract_arc_newsletter.return_value = [
            {
                "title": "AI Startup Raises $10M in Series A",
                "link": "https://example.com/ai-startup-series-a",
                "summary": "The AI company XYZ has raised $10 million to expand its operations...",
                "source": "ARC Newsletter",
                "tags": ["funding", "AI", "startup"],
            }
        ]
        mocked_post_response = {
            "documents": [
                {
                    "id": "1",
                    "entities": [
                        {
                            "text": "OpenAI",
                            "category": "Organization",
                            "confidenceScore": 0.99,
                        },
                        {
                            "text": "Microsoft",
                            "category": "Organization",
                            "confidenceScore": 0.98,
                        },
                    ],
                }
            ]
        }
        mock_response = MagicMock()
        mock_response.json.return_value = mocked_post_response
        mock_post_requests.return_value = mock_response

        mock_html_response = MagicMock()
        mock_html_response.content = b"""
            <html>
                <head><meta charset="utf-8"></head>
                <body><h1>Sample Page</h1></body>
            </html>
        """
        event = {
            "input_event_file_name": "test.json",
        }
        with patch(
            "utils.general_utils.requests.get", return_value=mock_html_response
        ):
            res = run_newsletter_handler(event, None)
        self.assertEqual(res, [])
