import unittest
from datetime import datetime
from unittest.mock import patch

import service.base_parser
from handlers.ds_rss_news_feed_handler import ds_rss_feed_handler
from utils.exceptions.exceptions import CustomException


class TestNewsFeedInvokerHandler(unittest.TestCase):
    """
    Functional Testcases for the handler ds_rss_feed_handler.
    """

    def setUp(self):
        pass

    def tearDown(self):
        pass

    @patch(
        "handlers.ds_rss_news_feed_handler.DSRSSParser.execute_get_rss_feed"
    )
    def test_exception(self, mock_service):
        event = None
        context = None
        mock_service.side_effect = CustomException("Error")
        res = ds_rss_feed_handler(event=event, context=context)
        self.assertRaises(CustomException)
        self.assertEqual(
            res,
            {
                "body": '"RSS News feed parser failed with error CustomException({})"',
                "statusCode": 500,
            },
        )

    @patch(
        "service.ds_rss_feed_parser.ds_rss_parser.DS_RSS_FEEDS",
        new=[
            {
                "SOURCE": "Inc42",
                "URL": "",
                "DATABASE": "NEWS_NOSQL",
                "DATE_OBJECT_PATTERN": "%a, %d %b %Y %H:%M:%S %z",
            }
        ],
    )
    @patch.object(service.base_parser.BaseParser, "get")
    @patch("service.ds_rss_feed_parser.ds_rss_parser.feedparser.parse")
    @patch("service.audit_service.boto3.client")
    def test_skipping(self, mock_sqs, mock_parse, mock_get_companies):
        event = None
        context = None
        time_data = datetime.now().astimezone()
        formatted_str = time_data.strftime("%a, %d %b %Y %H:%M:%S %z")
        dt = datetime.strptime(formatted_str, "%a, %d %b %Y %H:%M:%S %z")
        formatted_time = dt.strftime("%a, %d %b %Y %H:%M:%S %z")
        mock_sqs.send_message.return_value = None
        mock_parse.return_value = {
            "entries": [
                {
                    "title": "Some News Title asd",
                    "link": "http://example.com/news-1",
                    "published": formatted_time,
                }
            ]
        }
        mock_get_companies.return_value = [
            {
                "name": "sample name",
                "cin": "sample cin",
                "alias_name": ["asd", "123"],
            }
        ]
        res = ds_rss_feed_handler(event=event, context=context)
        self.assertEqual(res, {"body": '"Completed"', "statusCode": 200})

    @patch(
        "service.ds_rss_feed_parser.ds_rss_parser.DS_RSS_FEEDS",
        new=[
            {
                "SOURCE": "Inc42",
                "URL": "",
                "DATABASE": "NEWS_NOSQL",
                "DATE_OBJECT_PATTERN": "%a, %d %b %Y %H:%M:%S %z",
            }
        ],
    )
    @patch.object(service.base_parser.BaseParser, "get")
    @patch("service.ds_rss_feed_parser.ds_rss_parser.feedparser.parse")
    @patch("service.audit_service.boto3.client")
    def test_missing_fields(self, mock_sqs, mock_parse, mock_get_companies):
        event = None
        context = None
        time_data = datetime.now().astimezone()
        formatted_str = time_data.strftime("%a, %d %b %Y %H:%M:%S %z")
        dt = datetime.strptime(formatted_str, "%a, %d %b %Y %H:%M:%S %z")
        formatted_time = dt.strftime("%a, %d %b %Y %H:%M:%S %z")
        mock_sqs.send_message.return_value = None
        mock_parse.return_value = {
            "entries": [
                {
                    "title": "Some News Title asd",
                    "link": "http://example.com/news-1",
                    "published": formatted_time,
                }
            ]
        }
        first_get_resp = [
            {"name": "", "cin": "sample cin", "alias_name": ["asd", "123"]}
        ]
        second_get_resp = None
        mock_get_companies.side_effect = [first_get_resp, second_get_resp]
        res = ds_rss_feed_handler(event=event, context=context)
        self.assertEqual(res, {"body": '"Completed"', "statusCode": 200})

    @patch(
        "service.ds_rss_feed_parser.ds_rss_parser.DS_RSS_FEEDS",
        new=[
            {
                "SOURCE": "Inc42",
                "URL": "",
                "DATABASE": "NEWS_NOSQL",
                "DATE_OBJECT_PATTERN": "%a, %d %b %Y %H:%M:%S %z",
            }
        ],
    )
    @patch.object(service.base_parser.BaseParser, "get")
    @patch("service.ds_rss_feed_parser.ds_rss_parser.feedparser.parse")
    @patch("service.audit_service.boto3.client")
    def test_success(self, mock_sqs, mock_parse, mock_get_companies):
        event = None
        context = None
        time_data = datetime.now().astimezone()
        formatted_str = time_data.strftime("%a, %d %b %Y %H:%M:%S %z")
        dt = datetime.strptime(formatted_str, "%a, %d %b %Y %H:%M:%S %z")
        formatted_time = dt.strftime("%a, %d %b %Y %H:%M:%S %z")
        mock_sqs.send_message.return_value = None
        mock_parse.return_value = {
            "entries": [
                {
                    "title": "Some News Title asd",
                    "link": "http://example.com/news-1",
                    "published": formatted_time,
                }
            ]
        }
        first_get_resp = [
            {
                "name": "sample name",
                "cin": "sample cin",
                "alias_name": ["asd", "123"],
            }
        ]
        second_get_resp = None
        mock_get_companies.side_effect = [first_get_resp, second_get_resp]
        res = ds_rss_feed_handler(event=event, context=context)
        self.assertEqual(res, {"body": '"Completed"', "statusCode": 200})
