import json
import os
import time
import unittest
from unittest.mock import patch

from handlers.mail_news_handlers import run_mail_news_handler


class TestRunMailNewsHandler(unittest.TestCase):
    """
    Testcases for the handler run_mail_news_handler
    """

    def setUp(self):
        self.test_file = "test.json"
        self.test_no_config_file = "test_no_config.json"
        dummy_data = {
            "news_alert_configs": [
                {
                    "alert_source": "gmail",
                    "alert_email": "email id",
                    "alert_password": "password",
                    "alert_name": "company",
                    "alert_search_criteria": {
                        "from_address": "",
                        "subject": "Google Alerts",
                    },
                    "alert_date_format": "%a, %d %b %Y %H:%M:%S %z",
                }
            ]
        }
        with open(self.test_file, "w", encoding="utf-8") as f:
            json.dump(dummy_data, f)

    def tearDown(self):
        # Retry logic to handle potential file lock issues
        retries = 3
        while retries > 0:
            try:
                if os.path.exists(self.test_file):
                    os.remove(self.test_file)
                if os.path.exists(self.test_no_config_file):
                    os.remove(self.test_no_config_file)
                break
            except PermissionError:
                retries -= 1
                time.sleep(0.1)

    def test_no_filename(self):
        event = {}
        res = run_mail_news_handler(event, None)
        self.assertEqual(res, ["Input filename missing"])

    def test_no_config(self):
        dummy_data_no_config = {"news_alert_configs": []}
        with open(self.test_no_config_file, "w", encoding="utf-8") as f:
            json.dump(dummy_data_no_config, f)
        event = {
            "input_event_file_name": "test_no_config.json",
        }
        res = run_mail_news_handler(event, None)
        self.assertEqual(res, [])

    @patch("service.mail.gmail_service.imaplib.IMAP4_SSL")
    def test_success(self, mock_imap):
        mock_imap = mock_imap.return_value
        mock_imap.login.return_value = None
        mock_imap.search.return_value = "OK", [b"1 2 3 4 5"]
        mock_imap.fetch.return_value = "OK", [
            (
                b"1 (RFC822 {342})",
                b"From: <EMAIL>\r\n"
                b"To: <EMAIL>\r\n"
                b"Subject: Google Alerts - Test Subject\r\n"
                b"Date: Wed, 23 Apr 2025 10:00:00 +0000\r\n"
                b'Content-Type: text/plain; charset="UTF-8"\r\n'
                b"\r\n"
                b"This is a test email body from Google Alerts.\r\n",
            )
        ]
        event = {
            "input_event_file_name": "test.json",
        }
        res = run_mail_news_handler(event, None)
        self.assertEqual(res, [])
