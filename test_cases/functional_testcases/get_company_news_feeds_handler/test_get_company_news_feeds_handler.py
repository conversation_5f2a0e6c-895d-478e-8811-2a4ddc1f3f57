import copy
import json
import unittest
from unittest.mock import MagicMock
from unittest.mock import patch

import config
from constants.api_parameters import APIParameters
from handlers.get_company_news_feeds_handler import (
    get_company_news_feeds_handler,
)
from infrastructure.repository.base_repository import BaseRepository
from test_cases.helpers.input_data import COMPANY_INFO
from test_cases.helpers.input_data import (
    company_news_feed_handler_bing_event_body,
)
from test_cases.helpers.input_data import (
    company_news_feed_handler_diffbot_event_body,
)
from test_cases.helpers.input_data import (
    company_news_feed_handler_google_event_body,
)
from test_cases.helpers.input_data import (
    company_news_feed_handler_missing_event_body,
)
from test_cases.helpers.input_data import (
    company_news_feed_handler_missing_republish_after_body,
)
from test_cases.helpers.input_data import (
    company_news_feed_handler_missing_republish_till_body,
)
from test_cases.helpers.input_data import (
    company_news_feed_handler_newsdata_event_body,
)
from test_cases.helpers.input_data import db_entry_for_news_test


class TestGetCompanyNewsFeedHandler(unittest.TestCase):
    """
    Unit tests for the methods in the Get Company News Feed Handler.

    This testcase suite verifies the functionality of methods within the news_feed_invoker_handler.

    """

    def setUp(self):
        self._base_repo = BaseRepository(config.DEFAULT_TIMEOUT_MS)
        info_data = copy.deepcopy(COMPANY_INFO)
        info_data.append(db_entry_for_news_test)
        self._base_repo.insert(
            collection_name=APIParameters.COMPANY_INFO_COLLECTION.value,
            document=info_data,
        )

        self.boto_client_patcher = patch("boto3.client")
        self.requests_get_patcher = patch("requests.get")
        self.requests_post_patcher = patch("requests.post")

        # Start the patches
        self.mock_boto_client = self.boto_client_patcher.start()
        self.mock_get = self.requests_get_patcher.start()
        self.mock_post = self.requests_post_patcher.start()

        # Mocking requests.get response
        self.mock_response = MagicMock()
        self.mock_response.status_code = 200
        self.mock_response.url = "https://example.com/api?query=test"
        self.mock_response.json.return_value = {
            "news": [{"title": "Sample News"}],
            "value": [{"url": "testweb.com", "name": "hello world"}],
            "url": "https://example.com/api?param=value",
        }
        self.mock_get.return_value = self.mock_response
        self.mock_post.return_value = self.mock_response

        # Mocking boto3 SQS client
        self.mock_sqs_client = MagicMock()
        self.mock_boto_client.return_value = self.mock_sqs_client
        self.mock_sqs_client.send_message.return_value = {
            "MessageId": "12345",
            "ResponseMetadata": {"HTTPStatusCode": 200},
        }
        self.mock_sqs_client.put_message.return_value = None

    def tearDown(self):
        self._base_repo.delete(
            collection_name=APIParameters.COMPANY_INFO_COLLECTION.value,
            query={},
        )

    def test_with_missing_data(self):
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        company_news_feed_handler_missing_event_body
                    ),
                },
            ]
        }
        get_company_news_feeds_handler(event=event, context=None)

    def test_with_missing_republish_after(self):
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        company_news_feed_handler_missing_republish_after_body
                    ),
                },
            ]
        }
        get_company_news_feeds_handler(event=event, context=None)

    def test_with_missing_republish_till(self):
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        company_news_feed_handler_missing_republish_till_body
                    ),
                },
            ]
        }
        get_company_news_feeds_handler(event=event, context=None)

    @patch("utils.aws_system_manager_utils.parameter_store_utils.ssm_client")
    def test_google_feed_source(self, mock_ssm_client):
        mock_ssm_client.get_parameter.return_value = {
            "Parameter": {
                "Value": json.dumps(
                    {
                        "google_flagged_timestamp": "2025-01-30 00:00:00",
                        "google_flagged": False,
                    }
                )
            }
        }
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        company_news_feed_handler_google_event_body
                    ),
                },
            ]
        }
        get_company_news_feeds_handler(event=event, context=None)
        self.mock_sqs_client.send_message.assert_called_once()

    def test_bing_feed_source(self):
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        company_news_feed_handler_bing_event_body
                    ),
                },
            ]
        }
        get_company_news_feeds_handler(event=event, context=None)
        self.mock_sqs_client.send_message.assert_called_once()

    def test_diffbot_feed_source(self):
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        company_news_feed_handler_diffbot_event_body
                    ),
                },
            ]
        }
        get_company_news_feeds_handler(event=event, context=None)
        self.mock_sqs_client.send_message.assert_called_once()

    @patch("service.news_sources.newsdata_service.requests")
    def test_newsdata_feed_source(self, mock_requests):
        event = {
            "Records": [
                {
                    "body": json.dumps(
                        company_news_feed_handler_newsdata_event_body
                    ),
                },
            ]
        }
        mock_response = MagicMock()
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "status": "success",
            "totalResults": 10,
            "results": [
                {
                    "article_id": "test_id",
                    "title": "Cooper’s last stand",
                    "link": "www.test_link.com",
                    "description": "test description",
                    "content": "test content",
                    "pubDate": "2025-06-09 06:00:22",
                    "image_url": "www.test_image_link.com",
                    "source_name": "Politico Europe",
                    "language": "english",
                    "country": ["united kingdom"],
                    "category": ["politics"],
                }
            ],
        }
        mock_requests.get.return_value = mock_response
        get_company_news_feeds_handler(event=event, context=None)
        self.mock_sqs_client.send_message.assert_called_once()
