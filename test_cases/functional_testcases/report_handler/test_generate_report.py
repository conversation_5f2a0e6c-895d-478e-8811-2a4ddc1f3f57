import unittest
from unittest.mock import patch

import config
from constants.api_parameters import APIParameters
from handlers.report_handler import generate_report
from infrastructure.repository.base_repository import BaseRepository
from test_cases.helpers.input_helper import generate_news_report_helper
from utils.logger import get_logger

logger = get_logger(__name__)


class TestGenerateReport(unittest.TestCase):
    """Testcases for generate news report handler"""

    def setUp(self):
        self._base_repo = BaseRepository(config.DEFAULT_TIMEOUT_MS)
        self._base_repo.insert(
            collection_name=APIParameters.RAW_FEED_URL_COLLECTION.value,
            document=generate_news_report_helper,
        )

    def tearDown(self):
        self._base_repo.delete(
            collection_name=APIParameters.RAW_FEED_URL_COLLECTION.value,
            query={},
        )

    @patch("service.notification_service.lambda_client.invoke")
    @patch("service.report_service.write_to_bucket")
    def test_success(self, mock_write_to_bucket, mock_invoke_lambda):
        event = {}
        context = None
        mock_write_to_bucket.return_value = None
        mock_invoke_lambda.return_value = None
        generate_report(event, context)
        mock_write_to_bucket.assert_called()
        self.assertEqual(mock_write_to_bucket.call_count, 2)
        mock_invoke_lambda.assert_called_once()
