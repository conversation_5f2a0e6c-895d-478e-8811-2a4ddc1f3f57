import json
import sys

sys.path.append(".")

import unittest
from unittest.mock import patch
from handlers.scripts_handler.duplicate_cleanup_handler import (
    duplicate_cleanup_handler,
)


class TestDuplicateCheck(unittest.TestCase):
    """
    Unit test class to test the duplicate cleanup handler functionality.
    """

    def setUp(self):
        pass

    @patch("scripts.duplicate_check_and_delete.write_to_bucket")
    def test_duplicate_check_handler(self, mock_write_to_bucket):
        mock_write_to_bucket.return_value = None
        event = {"to_delete": False}
        context = {}

        response = duplicate_cleanup_handler(event, context)
        print(response)
        assert response["statusCode"] == 200
        assert json.loads(response["message"]) == "Completed"
