from datetime import datetime
from datetime import timedelta


COMPANY_INFO = [
    {
        "cin": "CIN1",
        "name": "Name1",
        "company": {
            "name": "Name1",
            "founder": ["Founder1"],
            "alias_name": ["<PERSON>wiggy", "Bundl Technologies"],
            "description": "Swiggy is a food company",
        },
        "domain": "First",
        "company_id": "companyid1",
        "classification": {"industries": ["i1", "i2"]},
    },
    {
        "cin": "CIN2",
        "name": "Name2",
        "company": {
            "name": "Name2",
            "founder": ["Founder2"],
            "description": "2 is some company",
        },
        "domain": "Second",
        "company_id": "companyid2",
        "classification": {"industries": ["i1", "i2"]},
    },
    {
        "cin": "CIN3",
        "name": "Name3",
        "company": {
            "name": "Name3",
            "founder": ["Founder3"],
            "description": "3 is some food company",
        },
        "domain": "Third",
        "company_id": "companyid3",
        "classification": {"industries": ["i1", "i2"]},
    },
]

db_entry_for_news_test = {
    "name": "Are<PERSON>",
    "cin": "U19119PN2020PTC190137",
    "is_active": True,
    "company": {
        "description": "Aretto is a footwear company specializing in adaptive shoes."
    },
    "classification": {"industries": ["Footwear", "Fashion"]},
    "company_news_name_docu": "Arettonamefornews",
    "company_master_summary": {"company_date_of_inc": "01-01-2021"},
    "social_network_links": {"diffbot_uri": "sample diffbot uri"},
    "name_for_news": "Arettonamefornews",
}

feed_parser_job_entry_for_news_test = {
    "cin": "U19119PN2020PTC190137",
    "news_till_date": datetime.now()
    - timedelta(hours=1),  # assuming news_fetch_buffer_hours > 1
    "parser_service": "Bing News",
    "news_from_date": "2020-02-06",
}

company_news_feed_handler_google_event_body = {
    "cin": "U19119PN2020PTC190137",
    "feed_source": "Google News",
    "service_id": "sample service id",
}

company_news_feed_handler_bing_event_body = {
    "name": "Aretto",
    "cin": "U19119PN2020PTC190137",
    "is_active": True,
    "company": {
        "description": "Aretto is a footwear company specializing in adaptive shoes."
    },
    "classification": {"industries": ["Footwear", "Fashion"]},
    "feed_source": "Bing News",
    "name_for_news": "Arettonamefornews",
    "company_master_summary": {"company_date_of_inc": "01-01-2021"},
    "service_id": "sample service id",
}

company_news_feed_handler_diffbot_event_body = {
    "cin": "U19119PN2020PTC190137",
    "feed_source": "Diffbot News",
    "service_id": "sample service id",
}

company_news_feed_handler_newsdata_event_body = {
    "cin": "U19119PN2020PTC190137",
    "feed_source": "Newsdata News",
    "service_id": "sample service id",
}

company_news_feed_handler_missing_event_body = {
    "name": "Aretto",
    "is_active": True,
    "company": {
        "description": "Aretto is a footwear company specializing in adaptive shoes."
    },
    "classification": {"industries": ["Footwear", "Fashion"]},
    "feed_source": "Bing News",
    "name_for_news": "Arettonamefornews",
    "company_master_summary": {"company_date_of_inc": "01-01-2021"},
    "service_id": "sample service id",
    "republish_after": "2025-01-01",
    "republish_till": "2025-01-01",
}

company_news_feed_handler_missing_republish_after_body = {
    "name": "Aretto",
    "cin": "U19119PN2020PTC190137",
    "is_active": True,
    "company": {
        "description": "Aretto is a footwear company specializing in adaptive shoes."
    },
    "classification": {"industries": ["Footwear", "Fashion"]},
    "feed_source": "Bing News",
    "name_for_news": "Arettonamefornews",
    "company_master_summary": {"company_date_of_inc": "01-01-2021"},
    "service_id": "sample service id",
    "republish_till": "2025-01-01",
}

company_news_feed_handler_missing_republish_till_body = {
    "name": "Aretto",
    "cin": "U19119PN2020PTC190137",
    "is_active": True,
    "company": {
        "description": "Aretto is a footwear company specializing in adaptive shoes."
    },
    "classification": {"industries": ["Footwear", "Fashion"]},
    "feed_source": "Bing News",
    "name_for_news": "Arettonamefornews",
    "company_master_summary": {"company_date_of_inc": "01-01-2021"},
    "service_id": "sample service id",
    "republish_after": "2025-01-01",
}
