from datetime import datetime
from datetime import timedelta

company_info_doc_1 = {
    "cin": "CIN1",
    "name": "<PERSON><PERSON>",
    "company": {
        "founder": ["Founder1"],
        "company_stage": "Series A",
        "description": "Good company",
    },
    "classification": {"industries": ["st_ee7058223596499ba408f55d16ee1bc8"]},
    "domain": "First",
    "is_active": True,
    "is_ready": True,
    "updated_at": datetime.strptime(
        "2025-08-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
}

company_info_doc_2 = {
    "cin": "CIN2",
    "name": "<PERSON>ith",
    "company": {
        "founder": ["Founder1"],
        "company_stage": "Series A",
        "description": "Good company",
    },
    "classification": {"industries": ["st_ee7058223596499ba408f55d16ee1bc8"]},
    "domain": "First",
    "is_active": True,
    "is_ready": True,
    "updated_at": datetime.strptime(
        "2023-08-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
}

company_info_doc_3 = {
    "cin": "CIN3",
    "name": "Swiggy",
    "company": {"alias_name": ["Swiggy"], "description": "Good company"},
    "classification": {"industries": ["st_ee7058223596499ba408f55d16ee1bc8"]},
    "is_active": True,
    "is_ready": True,
    "updated_at": datetime.strptime(
        "2024-08-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
}

company_info_doc_4 = {
    "cin": "CIN4",
    "name": "Random Company 1",
    "company": {
        "alias_name": ["Random Company 1"],
        "description": "Good company",
    },
    "classification": {"industries": ["st_ee7058223596499ba408f55d16ee1bc8"]},
    "is_active": True,
    "is_ready": True,
    "updated_at": datetime.strptime(
        "2022-08-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
}

company_info_doc_5 = {
    "cin": "CIN5",
    "name": "Random Company 2",
    "company": {"alias_name": ["Random Company 2"]},
    "is_active": True,
    "is_ready": True,
}
feed_job_parser_cin1 = {
    "cin": "CIN1",
    "parser_service": "Bing News",
    "date_of_inc": datetime.strptime(
        "2018-08-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "last_run_end_date": datetime.strptime(
        "2024-01-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "last_run_start_date": datetime.strptime(
        "2024-07-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "news_from_date": datetime.strptime(
        "2017-01-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "news_start_date": datetime.now() - timedelta(days=40),
    "news_till_date": datetime.now(),
}

feed_job_parser_cin3 = {
    "cin": "CIN3",
    "parser_service": "Bing News",
    "date_of_inc": datetime.strptime(
        "2018-08-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "last_run_end_date": datetime.strptime(
        "2024-01-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "last_run_start_date": datetime.strptime(
        "2024-07-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "news_from_date": datetime.strptime(
        "2017-01-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "news_start_date": datetime.now() - timedelta(days=40),
    "news_till_date": datetime.strptime(
        "2021-01-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
}

feed_job_parser_cin2 = {
    "cin": "CIN2",
    "parser_service": "Bing News",
    "date_of_inc": datetime.strptime(
        "2018-08-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "last_run_end_date": datetime.strptime(
        "2024-01-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "last_run_start_date": datetime.strptime(
        "2024-07-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "news_from_date": datetime.strptime(
        "2017-01-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
    "news_start_date": datetime.now() - timedelta(days=40),
    "news_till_date": datetime.strptime(
        "2024-01-06 05:21:32.490", "%Y-%m-%d %H:%M:%S.%f"
    ),
}

generate_news_report_helper = [
    {
        "cin": "test_cin",
        "name": "test name",
        "parser_service": "Bing News",
        "processed_date": datetime.now() - timedelta(days=1),
    },
    {
        "cin": "test_cin",
        "name": "test name",
        "parser_service": "Diffbot News",
        "processed_date": datetime.now() - timedelta(days=1),
    },
    {
        "cin": "test_cin",
        "name": "test name",
        "parser_service": "Bing News",
        "processed_date": datetime.now() - timedelta(days=1),
    },
    {
        "cin": "test_cin",
        "name": "test name",
        "parser_service": "Diffbot News",
        "processed_date": datetime.now() - timedelta(days=1),
    },
    {
        "cin": "test_cin",
        "name": "test name",
        "processed_date": datetime.now() - timedelta(days=1),
    },
]
