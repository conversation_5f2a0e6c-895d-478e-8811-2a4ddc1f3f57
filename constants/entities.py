from enum import Enum


class SQSEntities(Enum):
    """SQS entity attributes."""

    QUEUE_URL = "QueueUrl"
    MESSAGE_BODY = "MessageBody"
    MESSAGE_GROUP_ID = "MessageGroupId"


class ParserService(Enum):
    """Parser service types."""

    REPUBLISH: str = "republish-news"
    INVOKE_REPUBLISH: str = "invoke-republish-news"


class LambdaNameEntities(Enum):
    """Lambda function names."""

    GET_GOOGLE_NEWS_FEED = "get-google-news-feed"
    GET_NEWS_FEED = "get-news-feed"
    GET_DIFFBOT_NEWS_FEED = "get-diffbot-news-feed"
    GET_NEWSDATA_NEWS_FEED = "get-newsdata-news-feed"


class PrimarySourceEntities(Enum):
    """Primary source categories."""

    INTERNAL_DATA = "INTERNAL_DATA"
    NEWS = "NEWS"
    COMPANY_PROVIDED = "COMPANY_PROVIDED"


# further details on the audit schema can be found at https://bloktrek.atlassian.net/browse/PROD-810
class DataAuditLogs(Enum):
    """Audit log fields."""

    # mandatory fields
    LAMBDA = "lambda"
    PRIMARY_SOURCE = "primary_source"
    SECONDARY_SOURCE = "secondary_source"
    COMPANY_CIN = "company_cin"
    RESULT = "result"
    MESSAGE = "message"  # mandatory in case of failures
    START_DATE = "start_date"
    END_DATE = "end_date"
    PARSER_FLAGGED = "parser_flagged"
    FEED_AVAILABLE = "feed_available"
    MESSAGES_PUBLISHED = "messages_published"
    FEEDS_INSERTED = "feeds_inserted"
    PUBLISHING_URLS = "publishing_urls"
    SERVICE_ID = "service_id"

    # mongodb insertion
    SOURCE_LINK = "source_link"


class LambdaRunStatus(Enum):
    """Lambda run statuses."""

    SUCCESS = "success"
    FAILED = "failed"


class BingAPIParamEntities(Enum):
    """Bing API query parameters."""

    QUERY = "QUERY"
    COUNT = "COUNT"
    OFFSET = "OFFSET"
    NEWS_FRESHNESS = "NEWS_FRESHNESS"
    SEARCH_FRESHNESS = "SEARCH_FRESHNESS"
    SINCE = "SINCE"
    SORT = "SORT"
    RESPONSE_FILTER = "RESPONSE_FILTER"
    ANSWER_COUNT = "ANSWER_COUNT"
    PROMOTE = "PROMOTE"
    BASE_ENDPOINT = "BASE_ENDPOINT"
    SEARCH_SUFFIX = "SEARCH_SUFFIX"
    NEWS_SUFFIX = "NEWS_SUFFIX"
    HISTORICAL_START_DATE = "HISTORICAL_START_DATE"
    LANGUAGE = "LANGUAGE"


class BingFreshnessParamEntities(Enum):
    """Bing freshness parameters."""

    NOT_CONFIGURED = None
    MONTHLY = "Month"
    WEEKLY = "Week"
    DAILY = "Day"
    CUSTOM = "YYYY-MM-DD..YYYY-MM-DD"


class FeedSources(Enum):
    """Supported feed sources."""

    GOOGLE_NEWS = "Google News"
    BING_NEWS = "Bing News"
    BING_SEARCH = "Bing Search"
    DIFFBOT_NEWS = "Diffbot News"
    UNKNOWN = "Unknown"
    NEWSDATA = "Newsdata News"


image_key_mapping = {
    FeedSources.BING_NEWS.value: "image.thumbnail.contentUrl",
    FeedSources.BING_SEARCH.value: "thumbnailUrl",
}

published_date_key_mapping = {
    FeedSources.BING_NEWS.value: "datePublished",
    FeedSources.BING_SEARCH.value: "datePublished",
}


class GoogleNewsEntities(Enum):
    """Google News specific entities."""

    GOOGLE_NEWS_LIMIT_FLAG: str = "google_flagged"
    GOOGLE_NEWS_LIMIT_EXHAUST_TIMESTAMP: str = "google_flagged_timestap"
    GOOGLE_NEWS_LIMIT_DATA: str = "google_news_limits_data"
    GOOGLE_NEWS_REFRESH_HOUR_LIMIT: int = 24


class ParameterStoreEntities(Enum):
    """Parameter store fields."""

    PARAMETER: str = "Parameter"
    VALUE: str = "Value"
    VERSION: str = "Version"


class DateFormats(Enum):
    """Date format definitions."""

    DATETIME_FORMAT_WITH_SECONDS: str = "%Y-%m-%d %H:%M:%S"


class CredentialConstants(Enum):
    """Credential constants."""

    CONN_TIMEOUT: str = "CONN_TIMEOUT"


class OGConstants(Enum):
    """
    Constants used in OpenGraph
    """

    META: str = "meta"
    CONTENT: str = "content"
    PROPERTY: str = "property"
