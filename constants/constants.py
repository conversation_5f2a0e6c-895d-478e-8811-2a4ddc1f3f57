WEBLINK_DOWNLOAD_TIMEOUT = 100000
PDF_DOWNLOADER_WEBPAGE_LOAD_WAIT_TIME = 60
CHROMIUM_USER_AGENT = (
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
    "(KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
)
LOCAL_FILE_DOWNLOAD_PREFIX = "/tmp/"
CHROMIUM_BINARY_DOWNLOADER_BUCKET = "dev-privateblok-s3-artifacts"
CHROMIUM_BINARY_PATH = (
    "chromium-headless/stable-headless-chromium-amazonlinux-2.zip"
)
CHROMIUM_BINARY_ZIP_LOCAL_FILE_PATH = "/tmp/chrome.zip"
EXTRACTION_PATH = "/tmp/"
CHROMIUM_EXECUTABLE_PATH = "/tmp/headless-chromium"
LAMBDA_WRITABLE_PATH = "/tmp/"
CHROMEDRIVER_PATH = "/opt/chromedriver"
CHROME_BINARY_PATH = "/opt/chrome/chrome"
WKHTMLTOPDF_PATH = "/usr/local/bin/wkhtmltopdf"
PDF_EXTENSION = "pdf"
