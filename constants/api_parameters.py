from enum import Enum


class APIParameters(Enum):
    """
    General API Parameters
    """

    NEWS_INSERT_SQL = (
        "insert into news_feed(title, link, description, published_date, "
        "user_submitted, active, image_url, source, row_created) \
                    values (%s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP) \
                    on DUPLICATE KEY UPDATE row_updated = CURRENT_TIMESTAMP"
    )
    GOOGLE_NEWS_RSS_FEED = "GOOGLE_NEWS_RSS_FEED"
    COINDESK_RSS_FEED = "COINDESK_RSS_FEED"
    DATABASE = "DATABASE"
    URL = "URL"
    SQL_QUERY = "SQL_QUERY"
    SEARCH_KEYWORDS = "SEARCH_KEYWORDS"
    DATE_OBJECT_PATTERN = "DATE_OBJECT_PATTERN"
    SOURCE = "SOURCE"
    BASE_URL = "BASE_URL"
    EVENT = "Event"
    COMPANY_LIMIT = 1
    COMPANY_INFO_COLLECTION = "companyInfo"
    FEED_URL_COLLECTION = "feedURL"
    RAW_FEED_URL_COLLECTION = "rawFeedURL"
    COMPANY_NAME_FIELD = "name"
    COMPANY_CIN_FIELD = "cin"
    COMPANY_FIELD = "company"
    COMPANY_NEWS_NAME_DOCU_FIELD = "company.name_for_news"
    COMPANY_NEWS_NAME_FIELD = "name_for_news"
    ENTITY_ID_LIST = "entity_id_list"
    SINGLE_COMPANY_RUN = "single_company_run"
    RE_PUBLISH_AFTER_TIME = "republish_after"
    RE_PUBLISH_TILL_TIME = "republish_till"
    ONLY_DENOISED = "only_denoised"
    MAX_ARTICLES = "max_articles"
    COMPANY_MASTER_SUMMARY = "company_master_summary"
    INCORPORATION_DATE_FIELD = "company_date_of_inc"
    FEED_PARSER_JOB_STATUS_COLLECTION = "feedParserJobStatus"
    SCHEDULER_ARN = "SCHEDULER_ARN"
    AUDIT_TYPE = "newsMetrics"
    AUDIT_UUID_FIELD = "service_id"
    NEWS_INVOKER_SERVICE = "google-news-feed-invoker"
    GET_COMPANY_GOOGLE_FEED_SERVICE = "get-google-news-feed"
    GET_COMPANY_BING_NEWS_FEED_SERVICE = "get-bing-news-feed"
    GET_COMPANY_BING_SEARCH_FEED_SERVICE = "get-bing-search-feed"
    INDEPENDENT_PARSER_SERVICE = "rss-feeds-parser"
    INVOCATION_TYPE = "RequestResponse"
    MESSAGE = "message"
    PRIMARY_SOURCE = "primary_source"
    LAMBDA = "lambda"
    FEED_SOURCE = "feed_source"
    DESCRIPTION = "description"
    CLASSIFICATION = "classification"
    INDUSTRIES = "industries"
    IGNORE_DENOISED = "ignore_denoised"
    IS_READY = "is_ready"
    IS_ACTIVE = "is_active"
    NEWS_TILL_DATE = "news_till_date"
    NEWS_FROM_DATE = "news_from_date"
    SOCIAL_NETWORK_LINKS = "social_network_links"
    DIFFBOT_URI = "diffbot_uri"
    DIFFBOT_ENTITY_URI = "diffbot_entity_uri"
    WEBSITE = "website"
    DATA = "data"
    SERVICE_ID = "service_id"
    ENTITY_TYPE = "entity_type"
    THEME_ID = "theme_id"
    COMPANY_RECORD = "company_record"


class BingAPIParams(Enum):
    """
    Parameters for Bing API input.
    """

    QUERY = "q"
    COUNT = "count"
    OFFSET = "offset"
    FRESHNESS = "freshness"
    SINCE = "since"
    RESPONSE_FILTER = "responseFilter"
    ANSWER_COUNT = "answerCount"
    SORT = "sortBy"
    PROMOTE = "promote"
    SUB_HEADER = "Ocp-Apim-Subscription-Key"
    SEARCH_SERVICE = "SEARCH"
    NEWS_SERVICE = "NEWS"
    SET_LANGUAGE = "setLang"


class BingAPIResponse(Enum):
    """
    Entities for Bing API Response
    """

    WEBPAGES = "webPages"
    NEWS = "news"
    URL = "url"
    VALUE = "value"


class DiffbotAPIParams(Enum):
    """
    Parameters for Diffbot API output.
    """

    ENTITY = "entity"
    TITLE = "title"
    TEXT = "text"
    CATEGORIES = "categories"
    IMAGE = "image"
    TAGS = "tags"
    RESOLVED_PAGE_URL = "resolvedPageUrl"
    DATE = "date"
    TIMESTAMP = "timestamp"
    SITE_NAME = "siteName"
    PAGE_URL = "pageUrl"
    NAME = "name"
    LABEL = "label"


class NewsdataAPIParams(Enum):
    """
    Parameters for Newsdata API output.
    """

    STATUS: str = "status"
    RESULTS: str = "results"
    TOTAL_RESULTS: str = "totalResults"
    CONTENT: str = "content"
    TITLE: str = "title"
    CATEGORY: str = "category"
    LINK: str = "link"
    IMAGE_URL: str = "image_url"
    COUNTRY: str = "country"
    SOURCE_NAME: str = "source_name"
    PUBLISHED_DATE: str = "pubDate"
    DESCRIPTION: str = "description"
