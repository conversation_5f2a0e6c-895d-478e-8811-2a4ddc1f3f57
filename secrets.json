{"DEFAULT_TIMEOUT_MS": 5000, "NEWS_FETCH_BUFFER_HOURS": 10, "ADMIN_USER_ID": "", "ADMIN_ORG_ID": "", "PAGINATE_BING": false, "NEWS_FEED_SOURCES": ["Bing News", "Google News"], "INSERT_NEWS_FEED_QUEUE_BATCH_SIZE": 10, "NEWS_FEED_QUEUE_BATCH_SIZE": 3, "GMAIL_NEWS_SIZE": 30, "DIRECT_WEBLINK_PROCESSOR_SQS_URL": "", "ALT_FEED_PROCESSOR_SQS_URL": "", "WEBLINK_CONVERTED_PDF_QUEUE_URL": "", "INSERT_FEED_SERVICE_SQS_URL": "", "NEWS_FEED_SQS_URL": "", "AZURE_NER_ENDPOINT": "", "AZURE_NER_KEY": "", "DUPLICATE_S3_BACKUP_BUCKET": "duplicate-check-s3-backup", "FEEDS_BUCKET": "", "GOOGLE_NEWS_AUX_PARAMS": "&ceid=IN:en&hl=en-IN&gl=IN", "GOOGLE_NEWS_END_DATE": "+before:{end_date_str}", "GOOGLE_NEWS_URL": "{google_base_url}/search?q={company}+after:{start_date_str}", "GOOGLE_BASE_URL": "https://news.google.com/rss", "INCORPORATION_DATE_FORMAT": "%d-%m-%Y", "DIFFBOT_URL": "", "DIFFBOT_TOKENS": {"Demo Token": "demo"}, "NEWSDATA_TOKENS": {"Demo Token": "demo"}, "NEWSDATA_ENDPOINT": "", "MAX_DIFFBOT_ARTICLES_TO_FETCH": 50, "DATE_FORMAT": "%Y-%m-%d", "REPUBLISH_NEWS_ARN": "", "NOTIFICATION_LAMBDA_ARN": "", "WEBHOOK_URL": "", "AUDITING_SQS_URL": "", "DENOISING_URL_BATCH_SIZE": 1, "DENOISING_SQS_URL": "", "FEED_SOURCES_TO_IGNORE": ["github"], "DUPLICATE_CHECK_QUERY": [{"$match": {"news_link": {"$exists": true, "$ne": ""}}}, {"$group": {"_id": {"cin": "$cin", "news_link": "$news_link"}, "count": {"$sum": 1}, "documents": {"$push": "$$ROOT"}}}, {"$match": {"count": {"$gt": 1}}}, {"$project": {"_id": 1, "count": 1, "documents": 1}}], "BING_FEED_QUERY_PARAMS": {"HISTORICAL_START_DATE": "2016-01-01..", "NEWS_FRESHNESS": "Month", "SEARCH_FRESHNESS": "Month", "COUNT": 100, "OFFSET": 0, "SORT": "Relevance", "RESPONSE_FILTER": ["webpages", "news"], "ANSWER_COUNT": 1, "PROMOTE": "News", "LANGUAGE": "en"}, "CREDENTIALS": {"TOKEN_DETAILS": {"DRIVER": "mysql+pymysql", "HOST": "", "USER": "", "PASSWORD": "", "NAME": "token_details", "PORT": 3306, "LOGGING": false}, "NEWS_S3": {"DRIVER": "s3", "BUCKET_NAME": ""}, "NEWS_NOSQL": {"DRIVER": "mongodb", "DOCUMENT_DB_URI": "mongodb://localhost:27017", "DATABASE": "database", "CONN_TIMEOUT": 5000}}, "NEWS_FEEDS": {"DATABASE": "NEWS_NOSQL", "SCHEDULER_ARN": "", "DS_GOOGLE_NEWS_FEED": {"SOURCE": "Google News", "BASE_URL": "", "DATE_OBJECT_PATTERN": "%a, %d %b %Y %H:%M:%S %Z"}, "DS_BING_NEWS_FEED": {"SOURCE": "Bing News", "BASE_ENDPOINT": "https://api.bing.microsoft.com/", "NEWS_SUFFIX": "v7.0/news/search", "SEARCH_SUFFIX": "v7.0/search", "SUBSCRIPTION_KEYS": ["demokey"]}}, "DS_RSS_FEEDS": [{"SOURCE": "Inc42", "URL": "", "DATABASE": "NEWS_NOSQL", "DATE_OBJECT_PATTERN": "%a, %d %b %Y %H:%M:%S %z"}, {"SOURCE": "Entrackr", "URL": "", "DATABASE": "NEWS_NOSQL", "DATE_OBJECT_PATTERN": "%a, %d %b %Y %H:%M:%S %z"}, {"SOURCE": "TechCrunch", "URL": "", "DATABASE": "NEWS_NOSQL", "DATE_OBJECT_PATTERN": "%a, %d %b %Y %H:%M:%S %z"}, {"SOURCE": "YourStory", "URL": "", "DATABASE": "NEWS_NOSQL", "DATE_OBJECT_PATTERN": "%a, %d %b %Y %H:%M:%S %Z"}], "NEWS_PARSER_SERVICE_CONFIG": {"Bing News": {"DURATION_MONTHS": 6, "BATCH_SIZE": 1000}, "Google News": {"DURATION_MONTHS": 6, "BATCH_SIZE": 10}}}