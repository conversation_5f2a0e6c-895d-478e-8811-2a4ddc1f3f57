import time
import uuid
from datetime import datetime
from datetime import timedelta
from time import strptime

import feedparser

from config import DS_RSS_FEEDS
from constants.api_parameters import APIParameters
from service.audit_service import AuditService
from service.base_parser import BaseParser
from utils.date_utils import datetime_to_str
from utils.entities.collection_entities import FeedUrlEntities
from utils.entities.event_entities import ServiceEntities
from utils.general_utils import send_to_insert_feed_service_queue
from utils.logger import get_logger

logger = get_logger(__name__)


class DSRSSParser(BaseParser):
    """Parses and processes RSS feeds for companies' data."""

    def __init__(self, db_credentials):
        super().__init__(db_credentials)

        self._service_id = str(uuid.uuid4())
        self._audit_service = AuditService(self._service_id)

    def _feed_filter(self, feed_title, companies_data):
        companies_matched = []
        for company in companies_data:
            alias_names = company.get("alias_name", [company.get("name")])
            for alias_name in alias_names:
                if alias_name.lower().replace(
                    " ", ""
                ) in feed_title.lower().replace(" ", ""):
                    companies_matched.append(company)
        return companies_matched

    def _post_process(self, feed, date_object_pattern):
        link = feed["link"]
        published_date = datetime.strptime(
            feed["published"], date_object_pattern
        )
        title = feed["title"]
        return link, published_date, title

    def _get_companies(self):
        companies = self.get(
            data={
                "query": {
                    APIParameters.COMPANY_NAME_FIELD.value: {"$exists": True},
                    APIParameters.COMPANY_CIN_FIELD.value: {"$exists": True},
                    APIParameters.IS_READY.value: True,
                    APIParameters.IS_ACTIVE.value: True,
                },
                "collection_name": APIParameters.COMPANY_INFO_COLLECTION.value,
                "multiple_flag": True,
                "projection": {"_id": 0, "name": 1, "cin": 1, "alias_name": 1},
            }
        )
        return companies

    def _insert_feed(self, feed_url, date_object_pattern, source):
        start_date_obj = datetime.now() - timedelta(days=1)
        feeds = feedparser.parse(feed_url)
        companies = self._get_companies()
        duplicate_feed_count = 0
        feeds_published_count = 0
        for feed in feeds["entries"]:
            published_date = strptime(feed["published"], date_object_pattern)
            datetime_obj = datetime.fromtimestamp(time.mktime(published_date))
            companies_matched = self._feed_filter(feed["title"], companies)
            for company in companies_matched:
                (link, published_date, title) = self._post_process(
                    feed, date_object_pattern
                )
                existing_record = self.get(
                    data={
                        "query": {
                            "raw_link": link,
                            "cin": company[
                                APIParameters.COMPANY_CIN_FIELD.value
                            ],
                        },
                        "collection_name": APIParameters.FEED_URL_COLLECTION.value,
                    }
                )
                if (
                    datetime_obj < start_date_obj
                    or existing_record is not None
                ):
                    duplicate_feed_count += 1
                    logger.info(
                        f"Skipped '{title}' for {company[APIParameters.COMPANY_CIN_FIELD.value]}"
                        f"- {company[APIParameters.COMPANY_NAME_FIELD.value]} due to duplicacy"
                    )
                    continue
                cin = company.get(APIParameters.COMPANY_CIN_FIELD.value)
                name = company.get(APIParameters.COMPANY_NAME_FIELD.value)
                required_fields = [
                    cin,
                    name,
                    link,
                    published_date,
                    source,
                    title,
                ]
                if any(not field for field in required_fields):
                    logger.error(
                        f"Either cin={cin} or name={name} or link={link} "
                        f"or published_date={published_date} or source={source} "
                        f"or title={title} is missing"
                    )
                    continue
                message_body = {
                    FeedUrlEntities.NAME.value: name,
                    FeedUrlEntities.CIN.value: cin,
                    FeedUrlEntities.RAW_LINK.value: link,
                    FeedUrlEntities.PUBLISHED_DATE.value: datetime_to_str(
                        timestamp=published_date,
                        date_format="%Y-%m-%d %H:%M:%S",
                    ),
                    FeedUrlEntities.SOURCE.value: source,
                    FeedUrlEntities.PARSER_SERVICE.value: (
                        APIParameters.INDEPENDENT_PARSER_SERVICE.value
                    ),
                    FeedUrlEntities.RAW_TITLE.value: title,
                    FeedUrlEntities.NEWS_LINK.value: link,
                }
                feeds_published_count += 1
                send_to_insert_feed_service_queue(
                    urls=[message_body],
                    message_group_id=ServiceEntities.RSS_FEED_PARSER.value,
                )

        logger.info(f"{source} added {feeds_published_count} new feeds")

        audit_logs = {
            "source": source,
            "feeds_available": len(feeds["entries"]),
            "messages_published": feeds_published_count,
            "feeds_inserted": feeds_published_count,
            "service": APIParameters.INDEPENDENT_PARSER_SERVICE.value,
            "service_id": self._service_id,
            "duplicates_skipped": duplicate_feed_count,
        }

        self._audit_service.send_to_audit_queue(body=audit_logs)

    def execute_get_rss_feed(self):
        for ds_rss_feed in DS_RSS_FEEDS:
            logger.info(
                f"Running {ds_rss_feed[APIParameters.SOURCE.value]} parser"
            )
            feed_url = ds_rss_feed[APIParameters.URL.value]
            date_object_pattern = ds_rss_feed[
                APIParameters.DATE_OBJECT_PATTERN.value
            ]
            source = ds_rss_feed[APIParameters.SOURCE.value]
            self._insert_feed(feed_url, date_object_pattern, source)
