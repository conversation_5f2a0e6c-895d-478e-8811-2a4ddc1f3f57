import json
from datetime import date
from datetime import datetime
from datetime import timed<PERSON><PERSON>

import feedparser
import pymongo
from dateutil.relativedelta import relativedelta

from config import DATE_FORMAT
from config import NEWS_FEEDS
from config import NEWS_PARSER_SERVICE_CONFIG
from constants.api_parameters import APIParameters
from constants.api_parameters import BingAPIParams
from constants.entities import DataAuditLogs
from constants.entities import DateFormats
from constants.entities import FeedSources
from constants.entities import GoogleNewsEntities
from constants.entities import image_key_mapping
from constants.entities import LambdaNameEntities
from constants.entities import LambdaRunStatus
from constants.entities import PrimarySourceEntities
from constants.entities import published_date_key_mapping
from service.audit_service import AuditService
from service.base_parser import BaseParser
from service.feed_service import FeedService
from service.get_company_bing_services import BingServices
from service.news_sources.diffbot_news_service import DiffbotService
from service.news_sources.newsdata_service import NewsdataService
from utils.aws_system_manager_utils.parameter_store_utils import (
    fetch_parameter,
)
from utils.aws_system_manager_utils.parameter_store_utils import put_parameter
from utils.date_utils import datetime_to_str
from utils.entities.collection_entities import CompanyInfoEntities
from utils.entities.collection_entities import FeedParserJobStatusEntities
from utils.entities.collection_entities import FeedUrlEntities
from utils.entities.event_entities import EntityTypes
from utils.exceptions.exceptions import CustomException
from utils.general_utils import get_og_tags
from utils.general_utils import get_redirect_url
from utils.general_utils import publish_messages
from utils.general_utils import send_to_insert_feed_service_queue
from utils.logger import get_logger
from utils.message_format import UrlPublishMessageFormat
from utils.news_feed_utils import fetch_company_date_of_incorporation
from utils.news_feed_utils import get_feed_url
from utils.news_feed_utils import google_parser_existing_company_date_handling
from utils.news_feed_utils import is_feed_available
from utils.news_feed_utils import news_parser_existing_company_date_handling
from utils.s3 import create_news_file

logger = get_logger(__name__)


class FeedURLObject:
    """Class for Feed URL Object."""

    def __init__(self, **kwargs):
        self.title = kwargs.get("title")
        self.link = kwargs.get("link")
        self.news_link = kwargs.get("news_link")
        self.image_link = kwargs.get("image_link")
        self.published_date = kwargs.get("published_date")
        self.source = kwargs.get("source")

    def get_object(self):
        return {
            self.title,
            self.link,
            self.news_link,
            self.image_link,
            self.published_date,
            self.source,
        }


class GetCompanyNewsFeed(BaseParser):
    """Class to parse and republish Google News feed for companies."""

    def __init__(self, db_credentials, uuid):
        super().__init__(db_credentials)

        self._google_base_url = NEWS_FEEDS["DS_GOOGLE_NEWS_FEED"][
            APIParameters.BASE_URL.value
        ]
        self._date_object_pattern = NEWS_FEEDS["DS_GOOGLE_NEWS_FEED"][
            APIParameters.DATE_OBJECT_PATTERN.value
        ]
        self._service_id = uuid
        self._audit_service = AuditService(self._service_id)
        self._feed_service = FeedService()
        self._google_news_limit_data = {}

    def _extract_feed_details(self, feed):
        title = feed["title"].replace(f"- {feed['source']['title']}", "")
        base_link = feed["link"]
        published_date = datetime.strptime(
            feed["published"], self._date_object_pattern
        )
        source = feed["source"]["title"]

        return FeedURLObject(
            title=title,
            published_date=published_date,
            source=source,
            news_link=None,
            image_link=None,
            link=base_link,
        )

    def republish_feeds(self, **kwargs):
        """
        Utility method to republish feeds to the queue for given set of cins and after given date
        If no cins are given then all urls after given time are published to the queue
        """
        entity_id_list = kwargs.get(APIParameters.ENTITY_ID_LIST.value)
        start_time = kwargs.get("start_time")
        end_time = kwargs.get("end_time")
        max_articles_to_republish = kwargs.get("max_articles_to_republish")
        only_denoised = kwargs.get("only_denoised")
        logger.info(
            f"Started republishing the entity_id_list={entity_id_list} "
            f"with start time = {start_time} and only_denoised: {only_denoised}"
        )

        query = {
            "published_date": {"$gt": start_time},
            "entity_id": {"$exists": True},
        }
        if end_time:
            condition = query["published_date"]
            condition["$lt"] = end_time

        if entity_id_list and len(entity_id_list) > 0:
            query_entity_id = query.get("entity_id")
            query_entity_id.update({"$in": entity_id_list})
            query["entity_id"] = query_entity_id

        if only_denoised:
            query.update({"is_denoised": only_denoised})

        logger.info(f"Query for: {query} Limit: {max_articles_to_republish}")
        feed_urls_detail = self.get(
            data={
                "query": query,
                "collection_name": APIParameters.FEED_URL_COLLECTION.value,
                "projection": {
                    "news_link": 1,
                    "raw_link": 1,
                    "entity_id": 1,
                    "entity_type": 1,
                    "parser_service": 1,
                    "_id": 1,
                },
                "sort_order": [("published_date", pymongo.DESCENDING)],
                "limit": max_articles_to_republish,
                "multiple_flag": True,
            }
        )

        publishing_urls = []
        for feed_url_detail in feed_urls_detail:
            url_to_publish = feed_url_detail.get("news_link")
            if not url_to_publish:
                url_to_publish = feed_url_detail.get("raw_link")

            message_format = UrlPublishMessageFormat(
                entity_id=feed_url_detail.get("entity_id"),
                parser_service=feed_url_detail.get("parser_service"),
                service_id=str(feed_url_detail.get("_id")),
                url=url_to_publish,
                entity_type=feed_url_detail.get("entity_type"),
            )
            publishing_urls.append(message_format.to_response())

        messages_published_count = publish_messages(
            publishing_urls, message_group_id="REPUBLISH"
        )
        logger.info(
            f'Republishing for {entity_id_list if entity_id_list else "All"} '
            f"from {start_time} {messages_published_count} feeds republished"
        )

    def publish_news_link(self, **kwargs):
        company = kwargs.get("company")
        company_cin = kwargs.get("company_cin")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        all_feed_objects = kwargs.get("all_feed_objects")
        parser_service = kwargs.get("parser_service")
        publishing_urls = []
        feed_available = len(all_feed_objects) > 0

        urls_to_insert_queue = []
        for feed_obj in all_feed_objects:
            entity_type = EntityTypes.COMPANY.value
            published_date_str = datetime_to_str(
                timestamp=feed_obj.published_date,
                date_format="%Y-%m-%d %H:%M:%S",
            )
            s3_message = {
                FeedUrlEntities.CIN.value: company_cin,
                FeedUrlEntities.ENTITY_TYPE.value: entity_type,
                FeedUrlEntities.NAME.value: company,
                FeedUrlEntities.RAW_LINK.value: feed_obj.link,
                FeedUrlEntities.NEWS_LINK.value: feed_obj.news_link,
                FeedUrlEntities.IMAGE_LINK.value: feed_obj.image_link,
                FeedUrlEntities.PUBLISHED_DATE.value: published_date_str,
                FeedUrlEntities.SOURCE.value: feed_obj.source,
                FeedUrlEntities.RAW_TITLE.value: feed_obj.title,
                FeedUrlEntities.PARSER_SERVICE.value: parser_service,
                "service_id": self._service_id,
                FeedUrlEntities.RAW_DESCRIPTION.value: None,
                FeedUrlEntities.IGNORE_PUBLISHING.value: False,
            }
            message_data = create_news_file(
                s3_message, entity_type, company_cin
            )
            urls_to_insert_queue.append(message_data)
            message_format = UrlPublishMessageFormat(
                cin=company_cin,
                parser_service=parser_service,
                service_id=self._service_id,
                url=feed_obj.news_link,
                entity_type=EntityTypes.COMPANY.value,
            )

            publishing_url = message_format.to_response()
            publishing_urls.append(publishing_url)

        messages_published_count = len(publishing_urls)
        send_to_insert_feed_service_queue(urls=urls_to_insert_queue)
        self._send_to_audit_logs(
            company_cin=company_cin,
            start_date=start_date,
            end_date=end_date,
            parser_flagged=False,
            feed_available=feed_available,
            messages_published_count=messages_published_count,
            publishing_urls=publishing_urls,
        )
        return publishing_urls

    def _insert_google_feed(self, **kwargs):
        feed_url = kwargs.get("feed_url")
        company_cin = kwargs.get("company_cin")
        company = kwargs.get("company")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        logger.info(
            f"Getting the feeds from feed_url={feed_url}, "
            f"company_cin={company_cin}, company={company},"
            f" start_date={start_date}, end_date={end_date}"
        )

        feeds = feedparser.parse(feed_url)
        entries = []
        (feed_available, parser_flagged) = is_feed_available(feeds=feeds)
        if feed_available:
            entries = feeds.get("entries")

        logger.info(f"Total feeds available ={len(entries)}")

        all_feed_objects = []
        for feed in entries:
            feed_obj = self._extract_feed_details(feed)
            feed_obj.news_link, feed_obj.image_link = (
                self._get_article_details(feed_obj.link)
            )
            all_feed_objects.append(feed_obj)

        publishing_urls = self.publish_news_link(
            all_feed_objects=all_feed_objects,
            company=company,
            company_cin=company_cin,
            start_date=start_date,
            end_date=end_date,
            parser_service=APIParameters.GET_COMPANY_GOOGLE_FEED_SERVICE.value,
        )
        logger.info(
            f"{company}-{company_cin} {len(publishing_urls)} new feeds added"
        )
        if parser_flagged:
            logger.info(
                f"Google Parser has been flagged {feeds['feed']['summary']}"
            )
            self.__set_google_news_limit_flag()
            self.__set_google_news_timestamp()
            self.__update_value_to_system_manager()

            raise CustomException(
                f"Google Parser has been flagged {feeds['feed']['summary']}"
            )

    def __update_value_to_system_manager(self):
        logger.info(
            f"google news parser details:{self._google_news_limit_data} to be updated"
        )
        put_parameter(
            name=GoogleNewsEntities.GOOGLE_NEWS_LIMIT_DATA.value,
            value=json.dumps(self._google_news_limit_data),
            description="Google news limits and time stamp",
        )

    def __set_google_news_timestamp(self):
        now = datetime.now()
        self._google_news_limit_data[
            GoogleNewsEntities.GOOGLE_NEWS_LIMIT_EXHAUST_TIMESTAMP.value
        ] = now.replace(hour=0, minute=0, second=0, microsecond=0).strftime(
            DateFormats.DATETIME_FORMAT_WITH_SECONDS.value
        )

    def __reset_google_news_flag(self):
        flag_value = False
        google_news_limits_data = fetch_parameter(
            name=GoogleNewsEntities.GOOGLE_NEWS_LIMIT_DATA.value
        )
        if google_news_limits_data:
            google_news_limits_data_rsp = json.loads(google_news_limits_data)
        else:
            google_news_limits_data_rsp = {}
        google_news_limit_flag = google_news_limits_data_rsp.get(
            GoogleNewsEntities.GOOGLE_NEWS_LIMIT_FLAG.value, False
        )
        if google_news_limit_flag:
            logger.info(
                f"google news parser flag is set to :{google_news_limit_flag}"
            )
            google_news_exhausted_timestamp = datetime.strptime(
                google_news_limits_data_rsp.get(
                    GoogleNewsEntities.GOOGLE_NEWS_LIMIT_EXHAUST_TIMESTAMP.value
                ),
                DateFormats.DATETIME_FORMAT_WITH_SECONDS.value,
            )
            now = datetime.now()
            time_difference = now - google_news_exhausted_timestamp
            hours_difference = time_difference.total_seconds() / 3600
            if (
                hours_difference
                >= GoogleNewsEntities.GOOGLE_NEWS_REFRESH_HOUR_LIMIT.value
            ):
                self._google_news_limit_data[
                    GoogleNewsEntities.GOOGLE_NEWS_LIMIT_EXHAUST_TIMESTAMP.value
                ] = google_news_exhausted_timestamp.strftime(
                    DateFormats.DATETIME_FORMAT_WITH_SECONDS.value
                )
                self._google_news_limit_data[
                    GoogleNewsEntities.GOOGLE_NEWS_LIMIT_FLAG.value
                ] = False
                self.__update_value_to_system_manager()
            else:
                flag_value = True
        return flag_value

    def __set_google_news_limit_flag(self):
        logger.info(
            "setting google news flag to false as google news limit exhausted"
        )
        self._google_news_limit_data[
            GoogleNewsEntities.GOOGLE_NEWS_LIMIT_FLAG.value
        ] = True

    def _process_bing_feed(self, **kwargs):
        feed = kwargs.get("feed")
        feed_source = kwargs.get("feed_source")
        company = kwargs.get("company")
        company_cin = kwargs.get("company_cin")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        feed_service = kwargs.get("feed_service")
        logger.info(
            f"process bing feed for the feed_source={feed_source}, "
            f"company={company}, company_cin={company_cin},"
            f"start_date={start_date}, end_date={end_date}, "
            f"feed_service={feed_source}"
        )

        all_feed_objects = []
        for feed_entry in feed:
            feed_obj = self._parse_bing_feed(feed_entry, feed_source, end_date)
            all_feed_objects.append(feed_obj)

        publishing_urls = self.publish_news_link(
            all_feed_objects=all_feed_objects,
            company=company,
            company_cin=company_cin,
            start_date=start_date,
            end_date=end_date,
            parser_service=feed_service,
        )
        logger.info(
            f" company name = {company}, cin={company_cin} "
            f"urls={len(publishing_urls)} news feeds added"
        )

    def _parse_bing_feed(self, feed_entry, feed_source, end_date):
        news_link = feed_entry["url"]
        image_link = feed_entry.get(image_key_mapping.get(feed_source))
        corrected_datetime_string = feed_entry.get(
            published_date_key_mapping.get(feed_source)
        )
        if corrected_datetime_string:
            published_date = datetime.strptime(
                corrected_datetime_string.split(".")[0] + ".000000Z",
                "%Y-%m-%dT%H:%M:%S.%fZ",
            )
        else:
            published_date = end_date
        if "provider" in feed_entry:
            source = feed_entry["provider"][0]["name"]
        else:
            source = feed_source
        title = feed_entry["name"]
        return FeedURLObject(
            title=title,
            link=news_link,
            news_link=news_link,
            image_link=image_link,
            published_date=published_date,
            source=source,
        )

    def _upsert_job_status(self, **kwargs):
        company_name = kwargs.get("company_name")
        company_cin = kwargs.get("company_cin")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        feed_source = kwargs.get("feed_source")
        parser_job_status_doc = kwargs.get("parser_job_status_doc")
        logger.info(
            f"Updating the parser service status for the "
            f"company_name={company_name}, "
            f"cin={company_cin}, parser_service={feed_source}"
        )
        self._upsert_current_job_state(
            company_cin=company_cin,
            start_date=start_date,
            end_date=end_date,
            feed_source=feed_source,
            parser_job_status_doc=parser_job_status_doc,
        )

    def _send_to_audit_logs(self, **kwargs):
        company_cin = kwargs.get("company_cin")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        parser_flagged = kwargs.get("parser_flagged")
        feed_available = kwargs.get("feed_available")
        messages_published_count = kwargs.get("messages_published_count")
        publishing_urls = kwargs.get("publishing_urls")
        audit_logs = {
            DataAuditLogs.PRIMARY_SOURCE.value: PrimarySourceEntities.NEWS.value,
            DataAuditLogs.COMPANY_CIN.value: company_cin,
            "start_date": start_date.strftime("%Y-%m-%d %H:%M:%S"),
            "end_date": end_date.strftime("%Y-%m-%d %H:%M:%S"),
            "parser_flagged": parser_flagged,
            "feeds_available": feed_available,
            "messages_published": messages_published_count,
            "feeds_inserted": len(publishing_urls),
            DataAuditLogs.LAMBDA.value: LambdaNameEntities.GET_GOOGLE_NEWS_FEED.value,
        }
        if not parser_flagged:
            audit_logs[DataAuditLogs.RESULT.value] = (
                LambdaRunStatus.SUCCESS.value
            )
        else:
            audit_logs[DataAuditLogs.RESULT.value] = (
                LambdaRunStatus.FAILED.value
            )
            audit_logs[DataAuditLogs.MESSAGE.value] = (
                "Google Parser has been flagged"
            )
        self._audit_service.send_to_audit_queue(body=audit_logs)

    def _get_article_details(self, feed_link):
        image_link = None
        try:
            base_link = get_redirect_url(original_url=feed_link)
            parsed_og_tags = get_og_tags(url=base_link)

            image_link = parsed_og_tags.get("og:image")
            if image_link:
                image_link = image_link.replace("?p=facebook", "")

        except CustomException as error:
            base_link = feed_link
            logger.info(
                f"Image downloading failed, publishing the link [{feed_link}] to audit logs"
            )
            audit_logs = {
                DataAuditLogs.PRIMARY_SOURCE.value: PrimarySourceEntities.NEWS.value,
                DataAuditLogs.LAMBDA.value: LambdaNameEntities.GET_GOOGLE_NEWS_FEED.value,
                DataAuditLogs.RESULT.value: LambdaRunStatus.FAILED.value,
                DataAuditLogs.SOURCE_LINK.value: feed_link,
                DataAuditLogs.MESSAGE.value: f"{repr(error)}",
            }
            self._audit_service.send_to_audit_queue(body=audit_logs)
        return base_link, image_link

    def _get_last_job_state(self, company_cin, feed_source):
        last_job_state = self.get(
            data={
                "query": {
                    APIParameters.COMPANY_CIN_FIELD.value: company_cin,
                    "parser_service": feed_source,
                },
                "collection_name": APIParameters.FEED_PARSER_JOB_STATUS_COLLECTION.value,
                "projection": {
                    "_id": 0,
                },
            }
        )
        return last_job_state

    def _upsert_current_job_state(self, **kwargs):
        company_cin = kwargs.get("company_cin")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        feed_source = kwargs.get("feed_source")
        parser_job_status_doc = kwargs.get("parser_job_status_doc")
        dates_update = {
            FeedParserJobStatusEntities.LAST_RUN_START_DATE.value: start_date,
            FeedParserJobStatusEntities.LAST_RUN_END_DATE.value: end_date,
        }
        if parser_job_status_doc:
            dates_update.update(parser_job_status_doc)

        result = self.update(
            data={
                "filter_criteria": {
                    APIParameters.COMPANY_CIN_FIELD.value: company_cin,
                    FeedParserJobStatusEntities.PARSER_SERVICE.value: feed_source,
                },
                "update_operation": {"$set": dates_update},
                "collection_name": APIParameters.FEED_PARSER_JOB_STATUS_COLLECTION.value,
            }
        )
        if result.modified_count:
            logger.info(
                f"{APIParameters.FEED_PARSER_JOB_STATUS_COLLECTION.value} "
                f"updated for cin:{company_cin} for {feed_source}"
            )

    def _date_handling(self, company_cin, feed_source, company_record):
        start_date = None
        end_date = None
        parser_jobs_status_doc = {}
        last_triggered_time = self._get_last_job_state(
            company_cin=company_cin, feed_source=feed_source
        )
        if last_triggered_time is None:
            logger.info(
                f"cin:{company_cin} is a new company without triggered history"
            )
            if feed_source == FeedSources.GOOGLE_NEWS.value:
                start_date, end_date, parser_jobs_status_doc = (
                    self._google_parser_new_company_date_handling(
                        company_cin, feed_source, company_record
                    )
                )
            elif feed_source in [
                FeedSources.BING_NEWS.value,
                FeedSources.DIFFBOT_NEWS.value,
                FeedSources.NEWSDATA.value,
            ]:
                start_date, end_date, parser_jobs_status_doc = (
                    self._news_parser_new_company_date_handling(
                        company_cin, feed_source, company_record
                    )
                )
        else:
            logger.info(
                f"cin:{company_cin} was last triggered on :"
                f"{last_triggered_time}"
            )
            if feed_source == FeedSources.GOOGLE_NEWS.value:
                start_date, end_date, parser_jobs_status_doc = (
                    google_parser_existing_company_date_handling(
                        last_triggered_time, feed_source
                    )
                )
            elif feed_source in [
                FeedSources.BING_NEWS.value,
                FeedSources.DIFFBOT_NEWS.value,
                FeedSources.NEWSDATA.value,
            ]:
                start_date, end_date, parser_jobs_status_doc = (
                    news_parser_existing_company_date_handling(
                        last_triggered_time=last_triggered_time
                    )
                )
        logger.info(
            f"start_date:{start_date}, end_date:{end_date}, "
            f"parser_job_status_update:{parser_jobs_status_doc}"
        )
        return start_date, end_date, parser_jobs_status_doc

    def _news_parser_new_company_date_handling(
        self, company_cin, feed_source, company_record
    ):
        logger.info(
            f"setting dates for new company cin:{company_cin} and feed_source:{feed_source}"
        )
        parser_job_status_doc = {}
        current_datetime_now = datetime.now()
        company_date_of_inc = fetch_company_date_of_incorporation(
            company_cin=company_cin, company_record=company_record
        )
        parser_job_status_doc[
            FeedParserJobStatusEntities.DATE_OF_INC.value
        ] = company_date_of_inc
        if feed_source == FeedSources.NEWSDATA.value:
            parser_job_status_doc[
                FeedParserJobStatusEntities.NEWS_FROM_DATE.value
            ] = current_datetime_now - timedelta(weeks=24)
        else:
            parser_job_status_doc[
                FeedParserJobStatusEntities.NEWS_FROM_DATE.value
            ] = company_date_of_inc
        parser_job_status_doc[
            FeedParserJobStatusEntities.NEWS_TILL_DATE.value
        ] = current_datetime_now - timedelta(days=3)
        parser_job_status_doc[
            FeedParserJobStatusEntities.NEWS_START_DATE.value
        ] = current_datetime_now - timedelta(days=3)
        return (
            parser_job_status_doc[
                FeedParserJobStatusEntities.NEWS_FROM_DATE.value
            ],
            parser_job_status_doc[
                FeedParserJobStatusEntities.NEWS_TILL_DATE.value
            ],
            parser_job_status_doc,
        )

    def _google_parser_new_company_date_handling(
        self, company_cin, feed_source, company_record
    ):
        logger.info(
            f"setting dates for new company cin:{company_cin} and feed_source:{feed_source}"
        )
        parser_job_status_doc = {}
        start_date = datetime.now()
        parser_job_status_doc[
            FeedParserJobStatusEntities.DATE_OF_INC.value
        ] = fetch_company_date_of_incorporation(company_cin, company_record)

        parser_job_status_doc[
            FeedParserJobStatusEntities.NEWS_START_DATE.value
        ] = datetime.now()
        parser_job_status_doc[
            FeedParserJobStatusEntities.NEWS_TILL_DATE.value
        ] = datetime.now()

        end_date = start_date - relativedelta(
            months=NEWS_PARSER_SERVICE_CONFIG.get(feed_source).get(
                FeedParserJobStatusEntities.DURATION_MONTHS.value
            )
        )
        parser_job_status_doc[
            FeedParserJobStatusEntities.NEWS_FROM_DATE.value
        ] = end_date
        return (
            min(start_date, end_date),
            max(start_date, end_date),
            parser_job_status_doc,
        )

    def execute_news_feed_parser(self, **kwargs):
        company_cin: str = kwargs.get("company_cin")
        feed_source: str = kwargs.get("feed_source")
        start_date: date = kwargs.get("start_date")
        end_date: date = kwargs.get("end_date")
        company_record = self.get(
            data={
                "query": {APIParameters.COMPANY_CIN_FIELD.value: company_cin},
                "collection_name": APIParameters.COMPANY_INFO_COLLECTION.value,
                "projection": {
                    "_id": 0,
                    CompanyInfoEntities.CIN.value: 1,
                    CompanyInfoEntities.SOCIAL_NETWORK_LINKS.value: 1,
                    CompanyInfoEntities.NAME_FOR_NEWS.value: 1,
                    CompanyInfoEntities.NAME.value: 1,
                    CompanyInfoEntities.COMPANY_MASTER_SUMMARY.value: 1,
                    CompanyInfoEntities.COMPANY.value: 1,
                },
            }
        )
        if not company_record:
            logger.error(f"Company with cin={company_cin} does not exist")
            return
        company_name = company_record.get(CompanyInfoEntities.NAME.value)
        company_news_name = company_record.get(
            CompanyInfoEntities.NAME_FOR_NEWS.value, company_name
        )
        logger.info(
            f"fetching urls for company_name={company_name},  company_cin={company_cin}, "
            f"company_news_name={company_news_name}, "
            f"feed_source={feed_source}, start_date={start_date}, "
            f"end_date={end_date}"
        )
        if feed_source == FeedSources.GOOGLE_NEWS.value:
            google_news_allowed_flag = self.__reset_google_news_flag()
            if google_news_allowed_flag:
                scheduled_datetime = datetime.now().replace(
                    hour=0, minute=0, second=0, microsecond=0
                ) + timedelta(
                    hours=GoogleNewsEntities.GOOGLE_NEWS_REFRESH_HOUR_LIMIT.value
                )
                logger.info(
                    f"Google news limit is exhausted please try at :{scheduled_datetime}"
                )
                return

        if not self.company_description_exist(company_record=company_record):
            logger.error(
                f"company description does not exist for cin:{company_cin}"
            )
            return

        parser_job_status_doc = {}
        if not start_date or not end_date:
            logger.info(
                f"Getting the start date={start_date} and end date={end_date} "
                f"from job status as it is empty from "
                f"input request"
            )

            (start_date, end_date, parser_job_status_doc) = (
                self._date_handling(
                    company_cin=company_cin,
                    feed_source=feed_source,
                    company_record=company_record,
                )
            )
            if (
                start_date is None
                or end_date is None
                or parser_job_status_doc is None
            ):
                logger.info(
                    f"start_date:{start_date} or end_date:{end_date}"
                    f" or parser_job_status:{parser_job_status_doc} is None"
                    f"because data fetch has reached the date saturation limit."
                )
                return

        logger.info(
            f'{company_name}- start_date {start_date.strftime("%Y-%m-%d")} '
            f'end_date {end_date.strftime("%Y-%m-%d")}'
        )

        logger.info(f"started parser of {feed_source}")
        if feed_source == FeedSources.GOOGLE_NEWS.value:
            self.execute_google_feed_parser(
                company_cin=company_cin,
                company_news_name=company_news_name,
                company=company_name,
                start_date=start_date,
                end_date=end_date,
            )
        elif feed_source in (
            FeedSources.BING_NEWS.value,
            FeedSources.BING_SEARCH.value,
        ):
            self.execute_bing_feed_parser(
                company_cin=company_cin,
                company_news_name=company_news_name,
                company=company_name,
                start_date=start_date,
                end_date=end_date,
                feed_source=feed_source,
            )
        elif feed_source == FeedSources.DIFFBOT_NEWS.value:
            self.execute_diffbot_feed_parser(
                cin=company_cin,
                feed_source=feed_source,
                news_till_date=end_date,
                news_from_date=start_date,
                company_record=company_record,
            )
        elif feed_source == FeedSources.NEWSDATA.value:
            self.execute_newsdata_feed_parser(
                cin=company_cin,
                company_news_name=company_news_name,
                name=company_name,
                news_from_date=start_date,
                news_till_date=end_date,
            )
        self._upsert_job_status(
            company_name=company_name,
            company_cin=company_cin,
            start_date=start_date,
            end_date=end_date,
            feed_source=feed_source,
            parser_job_status_doc=parser_job_status_doc,
        )

    def execute_google_feed_parser(self, **kwargs):
        company_cin = kwargs.get("company_cin")
        company_news_name = kwargs.get("company_news_name")
        company = kwargs.get("company")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        feed_url = get_feed_url(company_news_name, start_date, end_date)
        self._insert_google_feed(
            feed_url=feed_url,
            company_cin=company_cin,
            company=company,
            start_date=start_date,
            end_date=end_date,
        )

    def execute_diffbot_feed_parser(self, **kwargs):
        cin = kwargs.get(APIParameters.COMPANY_CIN_FIELD.value)
        news_from_date = kwargs.get(APIParameters.NEWS_FROM_DATE.value)
        news_till_date = kwargs.get(APIParameters.NEWS_TILL_DATE.value)
        company_record = kwargs.get(APIParameters.COMPANY_RECORD.value)
        name = company_record.get(CompanyInfoEntities.NAME.value)
        logger.info(
            f"Fetching news from diffbot for company: name={name}, cin={cin}"
        )
        social_network_links = company_record.get(
            APIParameters.SOCIAL_NETWORK_LINKS.value, {}
        )
        diffbot_entity_uri = social_network_links.get(
            APIParameters.DIFFBOT_URI.value, ""
        )
        diffbot_service = DiffbotService(uuid=self._service_id)
        diffbot_service.diffbot_worker(
            cin=cin,
            name=name,
            news_from_date=news_from_date,
            news_till_date=news_till_date,
            diffbot_entity_uri=diffbot_entity_uri,
        )

    def execute_newsdata_feed_parser(self, **kwargs):
        cin = kwargs.get(APIParameters.COMPANY_CIN_FIELD.value)
        news_from_date = kwargs.get(APIParameters.NEWS_FROM_DATE.value)
        news_till_date = kwargs.get(APIParameters.NEWS_TILL_DATE.value)
        name = kwargs.get(APIParameters.COMPANY_NAME_FIELD.value)
        logger.info(
            f"Fetching news from newsdata for company: name={name}, cin={cin}"
        )
        newsdata_service = NewsdataService(uuid=self._service_id)
        newsdata_service.newsdata_worker(
            cin=cin,
            name=name,
            news_from_date=news_from_date,
            news_till_date=news_till_date,
        )

    def execute_bing_feed_parser(self, **kwargs):
        company_cin = kwargs.get("company_cin")
        company_news_name = kwargs.get("company_news_name")
        company = kwargs.get("company")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        feed_source = kwargs.get("feed_source")
        if feed_source == FeedSources.BING_NEWS.value:
            bing_service = BingServices(
                BingAPIParams.NEWS_SERVICE.value,
                NEWS_FEEDS["DS_BING_NEWS_FEED"]["SUBSCRIPTION_KEYS"],
            )
        elif feed_source == FeedSources.BING_SEARCH.value:
            bing_service = BingServices(
                BingAPIParams.SEARCH_SERVICE.value,
                NEWS_FEEDS["DS_BING_NEWS_FEED"]["SUBSCRIPTION_KEYS"],
            )
        else:
            logger.error(
                f"Invalid feed_source={feed_source} given for the company_cin={company_cin}, "
                f"company_news_name={company_news_name}, "
                f"company={company}, start_date={start_date}, "
                f"end_date={end_date}, feed_source={feed_source}"
            )
            return

        query_for_news = '"' + company + '"' + " " + "company"
        if feed_source == FeedSources.BING_NEWS.value:
            bing_feed = bing_service.bing_worker(query=query_for_news)
            self._process_bing_feed(
                feed=bing_feed,
                feed_source=feed_source,
                company=company,
                company_cin=company_cin,
                start_date=start_date,
                end_date=end_date,
                feed_service=APIParameters.GET_COMPANY_BING_NEWS_FEED_SERVICE.value,
            )
        elif feed_source == FeedSources.BING_SEARCH.value:
            bing_feed = bing_service.bing_worker(
                query_for_news,
                start_date.strftime(DATE_FORMAT),
                end_date.strftime(DATE_FORMAT),
            )
            self._process_bing_feed(
                feed=bing_feed,
                feed_source=feed_source,
                company=company,
                company_cin=company_cin,
                start_date=start_date,
                end_date=end_date,
                feed_service=APIParameters.GET_COMPANY_BING_SEARCH_FEED_SERVICE.value,
            )

    @staticmethod
    def company_description_exist(company_record):
        return (
            company_record.get(APIParameters.COMPANY_FIELD.value, {}).get(
                APIParameters.DESCRIPTION.value, None
            )
            if company_record
            else None
        )
