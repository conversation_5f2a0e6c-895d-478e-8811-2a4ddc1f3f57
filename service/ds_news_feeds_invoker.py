import json
import uuid
from datetime import datetime
from datetime import timed<PERSON>ta

from dateutil.relativedelta import relativedelta

from config import NEWS_FEED_QUEUE_BATCH_SIZE
from config import NEWS_FEED_SQS_URL
from config import NEWS_FEEDS
from config import NEWS_FETCH_BUFFER_HOURS
from config import NEWS_PARSER_SERVICE_CONFIG
from constants.api_parameters import APIParameters
from constants.entities import FeedSources
from service.audit_service import AuditService
from service.base_parser import BaseParser
from utils.entities.collection_entities import CompanyInfoEntities
from utils.entities.collection_entities import FeedParserJobStatusEntities
from utils.entities.collection_entities import FeedUrlEntities
from utils.general_utils import get_connection
from utils.general_utils import NEWS_FEED_SOURCES
from utils.general_utils import subtract_dates
from utils.logger import get_logger
from utils.sqs import SqsService

logger = get_logger(__name__)


class NewsCompanyInvoker(BaseParser):
    """
    Identifies companies for which we need to pull news from news services
    For these companies we invoke the news fetch lambda by passing the company name and CIN
    """

    def __init__(self, driver):
        super().__init__(driver)

        self._news_parser = NEWS_FEEDS[APIParameters.SCHEDULER_ARN.value]
        self._service_id = str(uuid.uuid4())
        self._audit_service = AuditService(self._service_id)

    def _get_companies(self, feed_source):
        """
        Get all companies from the company_info collection
        Then for each company check when we ran the last news jobs
        If the job ran before two days then consider it, else skip it

        If we are running the job for the very first time for a company then start
        from the incorporation date or 2018 which ever is later
        """
        invoking_company_list = []
        query = {
            APIParameters.COMPANY_NAME_FIELD.value: {"$exists": True},
            APIParameters.COMPANY_CIN_FIELD.value: {"$exists": True},
            CompanyInfoEntities.IS_ACTIVE.value: True,
            CompanyInfoEntities.IS_READY.value: True,
            f"{APIParameters.COMPANY_FIELD.value}.{APIParameters.DESCRIPTION.value}": {
                "$exists": True
            },
            f"{APIParameters.CLASSIFICATION.value}.{APIParameters.INDUSTRIES.value}": {
                "$exists": True,
                "$type": "array",
                "$ne": [],
            },
        }
        if feed_source == FeedSources.DIFFBOT_NEWS.value:
            query.update(
                {
                    f"{APIParameters.SOCIAL_NETWORK_LINKS.value}."
                    f"{APIParameters.DIFFBOT_URI.value}": {
                        "$exists": True,
                        "$ne": None,
                    }
                }
            )
        companies = list(
            self.get(
                data={
                    "query": query,
                    "collection_name": APIParameters.COMPANY_INFO_COLLECTION.value,
                    "multiple_flag": True,
                    "projection": {
                        "_id": 0,
                        APIParameters.COMPANY_CIN_FIELD.value: 1,
                        APIParameters.COMPANY_NEWS_NAME_DOCU_FIELD.value: 1,
                        APIParameters.COMPANY_NAME_FIELD.value: 1,
                        f"{APIParameters.COMPANY_MASTER_SUMMARY.value}."
                        f"{APIParameters.INCORPORATION_DATE_FIELD.value}": 1,
                    },
                }
            )
        )

        logger.info(f"Total active companies in the system ={len(companies)}")

        feed_job_details = self.get(
            data={
                "query": {
                    APIParameters.COMPANY_CIN_FIELD.value: {"$exists": True},
                    FeedUrlEntities.PARSER_SERVICE.value: feed_source,
                },
                "collection_name": APIParameters.FEED_PARSER_JOB_STATUS_COLLECTION.value,
                "projection": {"_id": 0},
                "multiple_flag": True,
            }
        )

        cin_based_details = {
            feed_job_detail.get(APIParameters.COMPANY_CIN_FIELD.value): {
                FeedParserJobStatusEntities.NEWS_TILL_DATE.value: feed_job_detail.get(
                    FeedParserJobStatusEntities.NEWS_TILL_DATE.value
                ),
                FeedParserJobStatusEntities.NEWS_FROM_DATE.value: feed_job_detail.get(
                    FeedParserJobStatusEntities.NEWS_FROM_DATE.value
                ),
            }
            for feed_job_detail in feed_job_details
        }
        for company in companies:
            company_cin = company.get(APIParameters.COMPANY_CIN_FIELD.value)
            news_till_date = cin_based_details.get(company_cin, {}).get(
                FeedParserJobStatusEntities.NEWS_TILL_DATE.value, None
            )
            news_from_date = cin_based_details.get(company_cin, {}).get(
                FeedParserJobStatusEntities.NEWS_FROM_DATE.value, None
            )

            if not news_from_date or not news_till_date:
                if feed_source in [
                    FeedSources.BING_NEWS.value,
                    FeedSources.DIFFBOT_NEWS.value,
                    FeedSources.NEWSDATA.value,
                ]:
                    news_till_date = datetime.now() - relativedelta(years=10)
                else:
                    news_till_date = datetime.now()
                news_from_date = news_till_date - relativedelta(
                    months=NEWS_PARSER_SERVICE_CONFIG.get(feed_source).get(
                        FeedParserJobStatusEntities.DURATION_MONTHS.value
                    )
                )

            company[FeedParserJobStatusEntities.NEWS_TILL_DATE.value] = (
                news_till_date
            )
            company[FeedParserJobStatusEntities.NEWS_FROM_DATE.value] = (
                news_from_date
            )
            company[FeedParserJobStatusEntities.NEWS_DELTA.value] = (
                subtract_dates(date1=news_till_date, date2=news_from_date)
            )

        feed_source_config = NEWS_PARSER_SERVICE_CONFIG.get(feed_source)
        batch_size = feed_source_config.get(
            FeedParserJobStatusEntities.BATCH_SIZE.value, 10
        )
        if feed_source == FeedSources.GOOGLE_NEWS.value:
            sorted_company_list = sorted(
                companies,
                key=lambda x: x[FeedParserJobStatusEntities.NEWS_DELTA.value],
            )
            invoking_company_list = sorted_company_list[:batch_size]
        elif feed_source in [
            FeedSources.BING_NEWS.value,
            FeedSources.DIFFBOT_NEWS.value,
            FeedSources.NEWSDATA.value,
        ]:
            filtered_companies = []
            for company in companies:
                company_cin = company.get(
                    APIParameters.COMPANY_CIN_FIELD.value
                )
                news_till_date = cin_based_details.get(company_cin, {}).get(
                    FeedParserJobStatusEntities.NEWS_TILL_DATE.value, None
                )
                if (
                    news_till_date
                    and news_till_date
                    > datetime.now() - timedelta(hours=NEWS_FETCH_BUFFER_HOURS)
                ):
                    logger.debug(f"Ignoring company: {company}.")
                else:
                    filtered_companies.append(company)
            sorted_company_list = sorted(
                filtered_companies,
                key=lambda x: x[
                    FeedParserJobStatusEntities.NEWS_TILL_DATE.value
                ],
                reverse=False,
            )
            invoking_company_list = sorted_company_list[:batch_size]

        logger.info(
            f"Total companies involving for now={len(invoking_company_list)}"
            f"for feed source:{feed_source} for "
            f"batch size:{batch_size}"
        )
        return invoking_company_list

    def _lambda_invoker(
        self, company_cin, company_news_name, company, feed_source
    ):
        client = get_connection("lambda")
        payload = {
            APIParameters.COMPANY_CIN_FIELD.value: company_cin,
            APIParameters.COMPANY_NEWS_NAME_FIELD.value: company_news_name,
            APIParameters.COMPANY_NAME_FIELD.value: company,
            APIParameters.FEED_SOURCE.value: feed_source,
            "service_id": self._service_id,
        }
        client.invoke(
            FunctionName=self._news_parser,
            InvocationType=APIParameters.EVENT.value,
            Payload=json.dumps(payload),
        )

    def execute_invoker(self, default_companies=None):
        # Get the companies for which we should invoke news
        for feed_source in NEWS_FEED_SOURCES:
            logger.info(
                f"Invoking the news parser for the feed source={feed_source}"
            )
            if default_companies and len(default_companies) > 0:
                companies = default_companies
            else:
                companies = self._get_companies(feed_source)

            count = 0

            logger.info(
                f"Triggering the news job for {len(companies)} companies"
            )
            all_cins = []
            for index, company in enumerate(companies):
                company_name = company.get(
                    APIParameters.COMPANY_NAME_FIELD.value
                )
                news_name = company.get(
                    APIParameters.COMPANY_FIELD.value, {}
                ).get(APIParameters.COMPANY_NEWS_NAME_FIELD.value)
                cin = company.get(APIParameters.COMPANY_CIN_FIELD.value)
                if not company_name or not cin:
                    logger.error(
                        f"Either company_name={company_name} "
                        f"or cin={cin} not provided"
                    )
                    continue
                message_body = {
                    APIParameters.COMPANY_CIN_FIELD.value: cin,
                    APIParameters.COMPANY_NEWS_NAME_FIELD.value: (
                        news_name if news_name else [company_name]
                    ),
                    APIParameters.COMPANY_NAME_FIELD.value: company_name,
                    APIParameters.FEED_SOURCE.value: feed_source,
                    "service_id": self._service_id,
                }

                message_body_feed_source = message_body.get(
                    APIParameters.FEED_SOURCE.value
                )

                SqsService.send_message_to_queue(
                    message=json.dumps(message_body),
                    queue_url=NEWS_FEED_SQS_URL,
                    message_group_id=f"{message_body_feed_source.replace(' ', '_')}"
                    f"_{str(index % NEWS_FEED_QUEUE_BATCH_SIZE)}",
                )
                count += 1
                all_cins.append(cin)

            logger.info(f"The company cins that get invoked={all_cins}")
            logger.info(
                f"Successfully invoked {count} companies for {feed_source}"
                f" parser "
            )
