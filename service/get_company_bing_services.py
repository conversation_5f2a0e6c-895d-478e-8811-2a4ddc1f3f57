import datetime
import http
import random
import time
import traceback

import requests

import config
from config import BING_FEED_QUERY_PARAMS
from config import NEWS_FEEDS
from constants.api_parameters import BingAPIParams
from constants.api_parameters import BingAPIResponse
from constants.entities import BingAPIParamEntities
from utils.logger import get_logger

logger = get_logger(__name__)


class BingServices:
    """Handles interactions with the Bing API to retrieve news and search results."""

    def __init__(self, service, subscription_keys):
        self._service = service
        self._subscription_keys = subscription_keys
        self._base_endpoint = NEWS_FEEDS["DS_BING_NEWS_FEED"][
            BingAPIParamEntities.BASE_ENDPOINT.value
        ]
        self._search_suffix = NEWS_FEEDS["DS_BING_NEWS_FEED"][
            BingAPIParamEntities.SEARCH_SUFFIX.value
        ]
        self._news_suffix = NEWS_FEEDS["DS_BING_NEWS_FEED"][
            BingAPIParamEntities.NEWS_SUFFIX.value
        ]
        self._endpoint = self.construct_feed_url()
        self._date_today = datetime.date.today().strftime("%Y-%m-%d")
        self._news_freshness = BING_FEED_QUERY_PARAMS[
            BingAPIParamEntities.NEWS_FRESHNESS.value
        ]
        self._historical_search_freshness = BING_FEED_QUERY_PARAMS[
            BingAPIParamEntities.HISTORICAL_START_DATE.value
        ]
        self._count = BING_FEED_QUERY_PARAMS[BingAPIParamEntities.COUNT.value]
        self._offset = BING_FEED_QUERY_PARAMS[
            BingAPIParamEntities.OFFSET.value
        ]
        self._since = time.mktime(datetime.datetime(2016, 1, 1).timetuple())
        self._sort = BING_FEED_QUERY_PARAMS[BingAPIParamEntities.SORT.value]
        self._response_filter = BING_FEED_QUERY_PARAMS[
            BingAPIParamEntities.RESPONSE_FILTER.value
        ]
        self._answer_count = BING_FEED_QUERY_PARAMS[
            BingAPIParamEntities.ANSWER_COUNT.value
        ]
        self._promote = BING_FEED_QUERY_PARAMS[
            BingAPIParamEntities.PROMOTE.value
        ]
        self._language = BING_FEED_QUERY_PARAMS[
            BingAPIParamEntities.LANGUAGE.value
        ]
        self._paginate = config.PAGINATE_BING
        self.news_list = []
        self.url_list = []
        self._result_hashes = set()
        self._finished = False
        self._random_subscription_key = random.choice(self._subscription_keys)

    def construct_feed_url(self):
        if self._service == BingAPIParams.NEWS_SERVICE.value:
            endpoint = self._base_endpoint + self._news_suffix
        else:
            endpoint = self._base_endpoint + self._search_suffix
        logger.info(f"bing endpoint being used to retrieve feed: {endpoint}")
        return endpoint

    def define_query_params(self, query, start_date=None, end_date=None):
        if isinstance(query, list):
            query = " %20OR%20 ".join(query)
        params = {
            BingAPIParams.QUERY.value: query,
            BingAPIParams.SET_LANGUAGE.value: self._language,
            BingAPIParams.OFFSET.value: self._offset,
            BingAPIParams.COUNT.value: self._count,
        }
        if self._service == BingAPIParams.NEWS_SERVICE.value:
            if self._news_freshness:
                params[BingAPIParams.FRESHNESS.value] = self._news_freshness
            if self._since:
                params[BingAPIParams.SINCE.value] = self._since
        else:
            params[BingAPIParams.FRESHNESS.value] = (
                start_date + ".." + end_date
            )
            params[BingAPIParams.RESPONSE_FILTER.value] = self._response_filter
            params[BingAPIParams.ANSWER_COUNT.value] = self._answer_count
            params[BingAPIParams.PROMOTE.value] = self._promote
        logger.info(f"query params being used are: {str(params)}")
        return params

    def retrieve_feed(self, query, start_date=None, end_date=None):
        logger.info(
            f"bing endpoint being used to retrieve feed: {self._endpoint}"
        )
        retries = 0
        max_retries = 3
        backoff_factor = 2
        params = {}
        headers = {
            BingAPIParams.SUB_HEADER.value: self._random_subscription_key
        }
        if self._service == BingAPIParams.NEWS_SERVICE.value:
            params = self.define_query_params(query)
        if self._service == BingAPIParams.SEARCH_SERVICE.value:
            params = self.define_query_params(query, start_date, end_date)
        while retries < max_retries:
            response = requests.get(
                self._endpoint, headers=headers, params=params, timeout=20
            )
            if response.status_code in [
                http.HTTPStatus.TOO_MANY_REQUESTS,
                http.HTTPStatus.INTERNAL_SERVER_ERROR,
            ]:
                logger.info(
                    f"Retry {retries + 1}: Received status {response.status_code}, retrying..."
                )
                retries += 1
                time.sleep(backoff_factor**retries)
                if retries < max_retries:
                    continue
            response.raise_for_status()
            logger.info(response.url)
            logger.info(
                f"Successfully made API call to {self._endpoint} with params={params}"
            )
            return response.json()
        # Code will never reach here. Added for pylint fix
        return None

    def retrieve_news_articles(self, query):
        try:
            logger.info(f"Retrieving news articles for the query: {query}")
            response_body = self.retrieve_feed(query)
            self.extract_urls(response_body[BingAPIResponse.VALUE.value])
            additional_news_list = response_body[BingAPIResponse.VALUE.value]
            return additional_news_list, self.url_list
        except Exception as ex:
            logger.error(
                "error in making API call to bing services: " + self._service
            )
            traceback.print_exc()
            raise ex

    def retreive_news_urls(self, query, start_date, end_date):
        try:
            logger.info(f"Retrieving webpage urls for the query: {query}")
            additional_news_list = []
            response_body = self.retrieve_feed(query, start_date, end_date)
            if BingAPIResponse.WEBPAGES.value in response_body:
                logger.info("found webpages in response body")
                for dict_ in response_body[BingAPIResponse.WEBPAGES.value][
                    BingAPIResponse.VALUE.value
                ]:
                    self.url_list.append(dict_[BingAPIResponse.URL.value])
                    additional_news_list.append(dict_)
            if BingAPIResponse.NEWS.value in response_body:
                logger.info("found news objects in response body")
                for dict_ in response_body[BingAPIResponse.NEWS.value][
                    BingAPIResponse.VALUE.value
                ]:
                    additional_news_list.append(
                        dict_[BingAPIResponse.URL.value]
                    )
            return additional_news_list, self.url_list
        except Exception as ex:
            logger.error(
                "error in handling API response body of bing services: "
                + self._service
            )
            traceback.print_exc()
            raise ex

    def extract_urls(self, news_list):
        for article in news_list:
            self.url_list.append(article[BingAPIResponse.URL.value])

    def add_unique_articles(self, additional_news_list):
        self.news_list.extend(additional_news_list)

    def calc_next_offset(self, resp_urls):
        before_adding = len(self._result_hashes)
        self._result_hashes.update((hash(i) for i in resp_urls))
        after_adding = len(self._result_hashes)
        if after_adding == before_adding:
            self._finished = True
        else:
            self._offset += len(resp_urls)

    def bing_worker(self, query, start_date=None, end_date=None):
        logger.info(f"Bing worker for the query={query}")
        while not self._finished:
            logger.debug("Total urls retrieved: " + str(len(self.url_list)))
            logger.debug(
                "Total articles retrieved: " + str(len(self.news_list))
            )
            if self._service == BingAPIParams.NEWS_SERVICE.value:
                additional_news_list, new_resp_urls = (
                    self.retrieve_news_articles(query)
                )
            else:
                additional_news_list, new_resp_urls = self.retreive_news_urls(
                    query, start_date, end_date
                )
            if additional_news_list:
                self.add_unique_articles(additional_news_list)
            if not self._paginate:
                logger.info(
                    f"Not collecting more articles because pagination flag is {self._paginate}"
                )
                break
            self.calc_next_offset(new_resp_urls)
        logger.info(
            f"All unique results for q={query} have been obtained "
            f"with count of {len(self.news_list)}"
        )
        return self.news_list
