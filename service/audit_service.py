import json
import traceback

import boto3
from botocore.exceptions import Client<PERSON>rror

from config import AUDITING_SQS_URL
from constants.api_parameters import APIParameters
from utils.logger import get_logger

logger = get_logger(__name__)


class AuditService:
    """Service for sending audit logs to an SQS queue for tracking and monitoring."""

    def __init__(self, uuid):
        self._sqs_client = boto3.client("sqs")
        self._sqs_url = AUDITING_SQS_URL
        self._service_id = uuid

    def send_to_audit_queue(self, body):
        body[APIParameters.AUDIT_UUID_FIELD.value] = self._service_id
        body = json.dumps(body)
        message_attributes = {}
        message_attributes["type"] = {
            "DataType": "String",
            "StringValue": APIParameters.AUDIT_TYPE.value,
        }
        self._publish_log(
            message_body=body, message_attributes=message_attributes
        )

    def _publish_log(self, message_body, message_attributes=None):

        response = None
        if not message_attributes:
            message_attributes = {}

        try:
            response = self._sqs_client.send_message(
                QueueUrl=self._sqs_url,
                MessageBody=message_body,
                MessageAttributes=message_attributes,
            )
        except ClientError as error:
            logger.error(
                f"Send message failed: {message_body} with "
                f"error {error.response['Error']['Message']}"
            )
            traceback.print_exc()
            return None

        return response
