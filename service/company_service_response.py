from utils.entities.collection_entities import CompanyInfoEntities


def get_company_details_response(companies):
    return [
        {
            CompanyInfoEntities.CIN.value: company.get(
                CompanyInfoEntities.CIN.value
            ),
            CompanyInfoEntities.NAME.value: company.get(
                CompanyInfoEntities.NAME.value
            ),
            CompanyInfoEntities.IS_ACTIVE.value: company.get(
                CompanyInfoEntities.IS_ACTIVE.value
            ),
            CompanyInfoEntities.IS_READY.value: company.get(
                CompanyInfoEntities.IS_READY.value
            ),
        }
        for company in companies
    ]
