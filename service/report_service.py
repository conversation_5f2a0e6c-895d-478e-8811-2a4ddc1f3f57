import io
from datetime import datetime
from datetime import time

import pandas as pd
from pymongo import MongoClient

import config
from config import CREDENTIALS
from config import NEWS_FEEDS
from config import NOTIFICATION_LAMBDA_ARN
from config import WEBHOOK_URL
from constants.api_parameters import APIParameters
from constants.entities import CredentialConstants
from constants.entities import FeedSources
from service.notification_service import NotificationService
from utils.entities.collection_entities import FeedUrlEntities
from utils.logger import get_logger
from utils.s3 import write_to_bucket

logger = get_logger(__name__)

credentials = CREDENTIALS.get(NEWS_FEEDS["DATABASE"])


class ReportService:
    """
    Service for generating and sending news report metrics based on articles data.
    """

    def __init__(self):
        self.client = MongoClient(
            credentials.get("DOCUMENT_DB_URI"),
            connectTimeoutMS=credentials.get(
                CredentialConstants.CONN_TIMEOUT.value
            ),
            serverSelectionTimeoutMS=credentials.get(
                CredentialConstants.CONN_TIMEOUT.value
            ),
        )
        self.database = self.client[credentials.get("DATABASE")]
        self.notification_service = NotificationService(
            webhook_url=WEBHOOK_URL, lambda_arn=NOTIFICATION_LAMBDA_ARN
        )

    def upload_news_report_file(self, start_date_str, end_date_str, denoised):
        start_date = datetime.combine(
            datetime.strptime(start_date_str, "%Y-%m-%d").date(), time.min
        )
        end_date = datetime.combine(
            datetime.strptime(end_date_str, "%Y-%m-%d").date(), time.max
        )
        collection_name = (
            APIParameters.FEED_URL_COLLECTION.value
            if denoised
            else APIParameters.RAW_FEED_URL_COLLECTION.value
        )
        query = {
            FeedUrlEntities.PROCESSED_DATE.value: {
                "$gte": start_date,
                "$lte": end_date,
            }
        }
        projection = {
            FeedUrlEntities.ENTITY_ID.value: 1,
            FeedUrlEntities.ENTITY_TYPE.value: 1,
            FeedUrlEntities.NAME.value: 1,
            FeedUrlEntities.NEWS_LINK.value: 1,
            FeedUrlEntities.PUBLISHED_DATE.value: 1,
            FeedUrlEntities.SOURCE.value: 1,
            FeedUrlEntities.RAW_TITLE.value: 1,
            FeedUrlEntities.PARSER_SERVICE.value: 1,
            FeedUrlEntities.PROCESSED_DATE.value: 1,
            FeedUrlEntities.ARTICLE_THEME.value: 1,
        }

        records = list(self.database[collection_name].find(query, projection))
        df = pd.DataFrame(records)

        number_of_articles = len(records)

        if FeedUrlEntities.PARSER_SERVICE.value not in df.columns:
            df[FeedUrlEntities.PARSER_SERVICE.value] = (
                FeedSources.UNKNOWN.value
            )

        csv_buffer = io.StringIO(newline="")
        df.sort_values(by=FeedUrlEntities.PARSER_SERVICE.value, inplace=True)
        df.to_csv(csv_buffer, index=False)

        if denoised:
            object_key = f"denoised_news_report/{start_date_str}_to_{end_date_str}_denoised.csv"
        else:
            object_key = (
                f"raw_news_report/{start_date_str}_to_{end_date_str}_raw.csv"
            )

        write_to_bucket(
            bucket_name=config.FEEDS_BUCKET,
            object_key=object_key,
            body_data=csv_buffer.getvalue(),
        )

        console_url = f"{config.NEWS_REPORT_CONSOLE_URL}{object_key}"
        return console_url, number_of_articles

    def generate_news_report(self, start_date_str, end_date_str):
        logger.info(
            f"Generating the news report for "
            f" start_date={start_date_str}, end_date={end_date_str}"
        )

        raw_url, number_of_raw_articles = self.upload_news_report_file(
            start_date_str=start_date_str,
            end_date_str=end_date_str,
            denoised=False,
        )
        denoised_url, number_of_denoised_articles = (
            self.upload_news_report_file(
                start_date_str=start_date_str,
                end_date_str=end_date_str,
                denoised=True,
            )
        )

        logger.info(
            f"News report uploaded. "
            f"Raw report url: {raw_url} "
            f"Denoised report url: {denoised_url}. "
            f"Sending notification..."
        )

        self.notification_service.send_teams_message(
            {
                "title": f"News Report for {start_date_str} to {end_date_str}",
                "body": f"Number of raw articles: {number_of_raw_articles}\n\n"
                f"Number of denoised articles: {number_of_denoised_articles}\n\n"
                f"Raw Report: {raw_url}\n\n"
                f"Denoised Report: {denoised_url}",
            }
        )

        logger.info("Finished report and sending notification")
