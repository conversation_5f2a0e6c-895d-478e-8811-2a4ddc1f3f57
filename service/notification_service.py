import json

import boto3

from utils.logger import get_logger

logger = get_logger(__name__)
lambda_client = boto3.client("lambda")


class NotificationService:
    """Service for sending notifications to Teams via Lambda."""

    def __init__(self, webhook_url, lambda_arn):
        self.webhook_url = webhook_url
        self.lambda_arn = lambda_arn

    def send_teams_message(self, message):
        event = {
            "resource": "msteams",
            "body": {"channel_webhook": self.webhook_url, "context": message},
        }

        logger.info(f"Payload={event}")

        response = lambda_client.invoke(
            FunctionName=self.lambda_arn,
            InvocationType="Event",
            Payload=json.dumps(event),
        )

        logger.info(response)
