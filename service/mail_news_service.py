from datetime import date
from datetime import datetime

from config import CREDENTIALS
from config import NEWS_FEEDS
from service.base_parser import Base<PERSON>arser
from service.company_service import CompanyService
from service.feed_service import FeedService
from service.mail.gmail_service import GmailService
from utils.date_utils import current_datetime
from utils.date_utils import datetime_to_str
from utils.entities.alert_entities import AlertEmailResponseEntities
from utils.entities.alert_entities import AlertEntities
from utils.entities.alert_entities import AlertInputEntities
from utils.entities.alert_entities import AlertNameEntities
from utils.entities.alert_entities import AlertSourceEntities
from utils.entities.collection_entities import CompanyInfoEntities
from utils.entities.collection_entities import FeedParserJobStatusEntities
from utils.entities.collection_entities import FeedUrlEntities
from utils.entities.gmail_entities import SearchCriteriaEntities
from utils.entities.gmail_entities import SearchResponseEntities
from utils.entities.parse_entities import ParserServiceEntities
from utils.general_utils import get_og_tags
from utils.general_utils import get_redirect_url
from utils.general_utils import send_to_insert_feed_service_queue
from utils.html_parser import parse_google_news_alert
from utils.logger import get_logger

logger = get_logger(__name__)


class MailNewsService(BaseParser):
    """
    A service for parsing and processing news alerts from emails,
    mapping them to companies, and publishing the results.
    """

    def __init__(self):
        super().__init__(db_credentials=CREDENTIALS[NEWS_FEEDS["DATABASE"]])
        self._feed_service = FeedService()
        self._company_service = CompanyService()
        self.__parser_service_name = (
            ParserServiceEntities.GOOGLE_MAIL_ALERTS.value
        )
        self.company_details_cache = {}

    def _get_parser_service_job_name(self, news_source: str, alert_name: str):
        return (
            f"{self.__parser_service_name}_{news_source}_{alert_name}".lower()
        )

    def update_alert_configs_with_last_run_details(self, alert_configs):
        logger.info(
            f"Updating the alert configs with last run details with alert_config={alert_configs}"
        )
        updated_alert_configs = []
        for alert_config in alert_configs:
            parser_job_name = self._get_parser_service_job_name(
                news_source=alert_config.get(
                    AlertInputEntities.ALERT_SOURCE.value
                ),
                alert_name=alert_config.get(
                    AlertInputEntities.ALERT_NAME.value
                ),
            )

            job_state_detail = self._feed_service.get_last_job_state(
                parser_service=parser_job_name, company_cin=None
            )

            if job_state_detail:
                alert_search_criteria = alert_config.get(
                    AlertInputEntities.ALERT_SEARCH_CRITERIA.value
                )
                from_date = job_state_detail.get(
                    FeedParserJobStatusEntities.LAST_RUN_END_DATE.value
                )
                run_details = job_state_detail.get(
                    FeedParserJobStatusEntities.LAST_RUN_DETAILS.value, {}
                )
                fetched_email_date = run_details.get(
                    FeedParserJobStatusEntities.EMAIL_DATE.value, from_date
                )

                alert_search_criteria.update(
                    {
                        SearchCriteriaEntities.EMAIL_NUMBER.value: run_details.get(
                            FeedParserJobStatusEntities.EMAIL_NUMBER.value, 0
                        )
                    }
                )
                alert_search_criteria.update(
                    {
                        SearchCriteriaEntities.FROM_DATE.value: fetched_email_date.strftime(
                            "%d-%b-%Y"
                        )
                    }
                )
                alert_config.update(
                    {
                        AlertInputEntities.ALERT_SEARCH_CRITERIA.value: alert_search_criteria
                    }
                )

            updated_alert_configs.append(alert_config)

        return updated_alert_configs

    def update_alert_job_state(
        self,
        alert_configs: list,
        start_date: date,
        end_date: date,
        run_details: dict,
    ):
        logger.info("Update the alert job state")
        for alert_config in alert_configs:
            parser_job_name = self._get_parser_service_job_name(
                news_source=alert_config.get(
                    AlertInputEntities.ALERT_SOURCE.value
                ),
                alert_name=alert_config.get(
                    AlertInputEntities.ALERT_NAME.value
                ),
            )
            self._feed_service.upsert_job_state(
                company_cin=None,
                parser_service=parser_job_name,
                start_date=start_date,
                end_date=end_date,
                run_details=run_details,
            )

    def parse_alert_email_body(self, email_data_list: list):
        logger.info("Parsing the alert email body")
        alerts_news = []

        for email_data in email_data_list:
            logger.info(
                f"Parsing the email body for the "
                f"email_id={email_data.get(AlertEmailResponseEntities.EMAIL_ID.value)}"
            )
            extracted_alerts = []
            alert_name = email_data.get(
                AlertEmailResponseEntities.ALERT_NAME.value
            )
            alert_body = email_data.get(AlertEmailResponseEntities.BODY.value)
            alert_date_str = email_data.get(
                AlertEmailResponseEntities.DATE.value
            )
            alert_subject = email_data.get(
                AlertEmailResponseEntities.SUBJECT.value
            )

            if alert_name == AlertNameEntities.ALERTS.value:
                extracted_alerts = parse_google_news_alert(
                    html_body=alert_body,
                    alert_name=alert_name,
                    alert_subject=alert_subject,
                )
            else:
                logger.error(
                    f"Invalid alert name={alert_name} provided in the config"
                )

            if extracted_alerts:
                for extracted_alert in extracted_alerts:
                    extracted_alert.update(
                        {AlertEntities.PUBLISHED_DATE.value: alert_date_str}
                    )

                alerts_news.extend(extracted_alerts)

        return alerts_news

    @staticmethod
    def convert_urls_to_permanent_url(alerts):
        logger.debug(
            f"Converting urls to permanent url for these url ={alerts}"
        )
        updated_alerts = []
        for alert in alerts:
            news_link = alert.get(AlertEntities.NEWS_LINK.value)
            news_image = alert.get(AlertEntities.NEWS_IMAGE.value)

            if news_link:
                alert.update(
                    {
                        AlertEntities.NEWS_LINK.value: get_redirect_url(
                            news_link
                        )
                    }
                )

            if not news_image:
                parsed_og_tags = get_og_tags(
                    url=alert.get(AlertEntities.NEWS_LINK.value)
                )
                news_image = parsed_og_tags.get("og:image")

            if news_image:
                alert.update(
                    {
                        AlertEntities.NEWS_IMAGE.value: get_redirect_url(
                            news_image
                        )
                    }
                )

            updated_alerts.append(alert)
        return updated_alerts

    def get_news_alerts(self, alert_configs):
        logger.info(f"Getting the alert for the alert configs={alert_configs}")
        email_data_list = []
        processed_alert_details = {}

        for alert_config in alert_configs:
            alert_source = alert_config.get(
                AlertInputEntities.ALERT_SOURCE.value
            )
            alert_name = alert_config.get(AlertInputEntities.ALERT_NAME.value)
            alert_email = alert_config.get(
                AlertInputEntities.ALERT_EMAIL.value
            )
            alert_password = alert_config.get(
                AlertInputEntities.ALERT_PASSWORD.value
            )
            alert_date_format = alert_config.get(
                AlertInputEntities.ALERT_DATE_FORMAT.value
            )
            alert_search_criteria = alert_config.get(
                AlertInputEntities.ALERT_SEARCH_CRITERIA.value
            )

            if (
                not alert_source
                or not alert_name
                or not alert_email
                or not alert_password
            ):
                logger.error(
                    f"Either alert_source={alert_source} or alert_name={alert_name} "
                    f"or alert_email={alert_email} or alert_password is empty"
                )
                continue

            if AlertSourceEntities.GMAIL.value == alert_source:
                gmail_service = GmailService(
                    username=alert_email, password=alert_password
                )
                gmail_service.select_mailbox(mailbox="INBOX")
                gmail_data_list = gmail_service.get_emails(
                    search_criteria=alert_search_criteria,
                    text_format=False,
                    newsletter_date_format=alert_date_format,
                )
                # [
                #     gmail_data.update(
                #         {
                #             AlertEmailResponseEntities.ALERT_NAME.value: alert_name
                #         }
                #     )
                #     for gmail_data in gmail_data_list
                # ]
                for gmail_data in gmail_data_list:
                    gmail_data.update(
                        {
                            AlertEmailResponseEntities.ALERT_NAME.value: alert_name
                        }
                    )
                email_data_list.extend(gmail_data_list)

        length_email_data_list = len(email_data_list)
        logger.info(
            f"Total email data list before parsing={length_email_data_list}"
        )

        if length_email_data_list:
            last_email_data = email_data_list[length_email_data_list - 1]
            processed_alert_details.update(
                {
                    FeedParserJobStatusEntities.EMAIL_NUMBER.value: int(
                        last_email_data.get(
                            SearchResponseEntities.EMAIL_ID.value
                        )
                    ),
                    FeedParserJobStatusEntities.EMAIL_DATE.value: last_email_data.get(
                        SearchResponseEntities.DATE.value
                    ),
                }
            )

        alerts = self.parse_alert_email_body(email_data_list=email_data_list)
        logger.info(f"Total news found from email={len(alerts)}")

        return (
            MailNewsService.convert_urls_to_permanent_url(alerts=alerts),
            processed_alert_details,
        )

    def map_news_alerts_to_company(self, alerts: list):
        alerts_with_cin = []
        logger.info("Map alerts to company")
        for alert in alerts:
            company_name = alert.get(AlertEntities.COMPANY_NAME.value)
            if company_name in self.company_details_cache:
                details = self.company_details_cache[company_name]
            else:
                details = self._company_service.get_company_details(
                    name=company_name
                )
                if not details:
                    logger.info(
                        f"Company={company_name} is not found in our database, please the"
                        f" company name if you wish to get the news"
                    )
                    continue
                self.company_details_cache[company_name] = details

            for detail in details:
                company_cin = {
                    AlertEntities.CIN.value: detail.get(
                        CompanyInfoEntities.CIN.value
                    ),
                    AlertEntities.COMPANY_NAME.value: detail.get(
                        CompanyInfoEntities.NAME.value
                    ),
                    FeedUrlEntities.IGNORE_PUBLISHING.value: not detail.get(
                        CompanyInfoEntities.IS_ACTIVE.value, False
                    ),
                }

                alerts_with_cin.append({**alert, **company_cin})

        return alerts_with_cin

    def ignore_duplicate_alerts(self, alerts):
        logger.info("Ignoring the duplicate alerts")
        duplicate_removed_alerts = []
        for alert in alerts:
            company_cin = alert.get(AlertEntities.CIN.value)
            news_link = alert.get(AlertEntities.NEWS_LINK.value)
            if not self._feed_service.check_duplicate_news(
                company_cin=company_cin,
                news_link=news_link,
                news_title=alert.get(AlertEntities.TITLE.value),
            ):
                duplicate_removed_alerts.append(alert)
            else:
                logger.info(
                    f"We found that the news_link={news_link} for the company_cin={company_cin}"
                )

        return duplicate_removed_alerts

    def insert_alerts(self, alerts: list):
        logger.info(f"Inserting alert with {len(alerts)}")
        urls_to_insert_queue = []
        for alert in alerts:
            company_cin = alert.get(AlertEntities.CIN.value)
            news_link = alert.get(AlertEntities.NEWS_LINK.value)
            published_date = alert.get(AlertEntities.PUBLISHED_DATE.value)

            if isinstance(published_date, datetime):
                published_date = datetime_to_str(
                    timestamp=published_date, date_format="%Y-%m-%d %H:%M:%S"
                )
            message_body = {
                FeedUrlEntities.CIN.value: company_cin,
                FeedUrlEntities.NAME.value: alert.get(
                    AlertEntities.COMPANY_NAME.value
                ),
                FeedUrlEntities.RAW_LINK.value: news_link,
                FeedUrlEntities.NEWS_LINK.value: news_link,
                FeedUrlEntities.IMAGE_LINK.value: alert.get(
                    AlertEntities.NEWS_IMAGE.value
                ),
                FeedUrlEntities.PUBLISHED_DATE.value: published_date,
                FeedUrlEntities.SOURCE.value: alert.get(
                    AlertEntities.NEWS_SOURCE.value
                ),
                FeedUrlEntities.RAW_TITLE.value: alert.get(
                    AlertEntities.TITLE.value
                ),
                FeedUrlEntities.RAW_DESCRIPTION.value: alert.get(
                    AlertEntities.DESCRIPTION.value
                ),
                FeedUrlEntities.PARSER_SERVICE.value: self.__parser_service_name,
                FeedUrlEntities.IGNORE_PUBLISHING.value: alert.get(
                    FeedUrlEntities.IGNORE_PUBLISHING.value
                ),
            }
            urls_to_insert_queue.append(message_body)

        send_to_insert_feed_service_queue(urls=urls_to_insert_queue)

    def run_mail_news(self, alert_configs: list):
        """
        1.Get last run config
        2.Get new emails based on config
        3.Map news alert to company
        4. Ignore duplicate alert
        5. Create alerts
        6. Publish alerts
        :param alert_configs:
        :return:
        """
        logger.info(f"Running the news alerts for the configs={alert_configs}")
        start_date = current_datetime()

        alert_configs = self.update_alert_configs_with_last_run_details(
            alert_configs=alert_configs
        )
        alerts, processed_alert_details = self.get_news_alerts(
            alert_configs=alert_configs
        )

        company_alerts = self.map_news_alerts_to_company(alerts=alerts)

        duplicate_removed_alerts = self.ignore_duplicate_alerts(
            alerts=company_alerts
        )
        self.insert_alerts(alerts=duplicate_removed_alerts)

        self.update_alert_job_state(
            alert_configs=alert_configs,
            start_date=start_date,
            end_date=current_datetime(),
            run_details=processed_alert_details,
        )
