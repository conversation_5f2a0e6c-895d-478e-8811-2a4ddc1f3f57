from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup

from utils.logger import get_logger

logger = get_logger(__name__)


class WebpageFeedParser:
    """
    Parser for parsing webpage feeds and collecting all the links on the page.
    Uses requests and BeautifulSoup.
    """

    def __init__(self):
        pass

    def parse(self, url):
        try:
            response = requests.get(url=url, timeout=20)
            all_links = set()
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, "html.parser")
                all_links = {
                    urljoin(url, a["href"])
                    for a in soup.find_all("a", href=True)
                }
            else:
                logger.error(
                    f"Failed to retrieve {url} - Status code: {response.status_code}"
                )
            logger.info(f"Collected {len(all_links)} links from {url}")
            return {url: all_links}
        except requests.RequestException as error:
            logger.error(f"Error fetching {url}: {error}")
            return {}
