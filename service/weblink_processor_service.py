import base64
import time
from tempfile import mkdtemp

import undetected_chromedriver as uc
from selenium.webdriver.chrome.options import Options

from constants import constants
from utils.exceptions.exceptions import CustomException
from utils.logger import get_logger

logger = get_logger(__name__)


class WeblinkProcessor:
    """
    Processes a weblink and converts the webpage to a PDF file.
    """

    def __init__(self):
        pass

    def process_weblink_to_pdf(self, weblink, local_download_path):
        try:
            chrome_options = Options()
            chrome_options.binary_location = constants.CHROME_BINARY_PATH
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1280x1696")
            chrome_options.add_argument("--single-process")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-dev-tools")
            chrome_options.add_argument("--no-zygote")
            chrome_options.add_argument(
                "--disable-features=VizDisplayCompositor"
            )
            chrome_options.add_argument(f"--user-data-dir={mkdtemp()}")
            chrome_options.add_argument(f"--data-path={mkdtemp()}")
            chrome_options.add_argument(f"--disk-cache-dir={mkdtemp()}")
            chrome_options.add_argument("--remote-debugging-port=9222")
            chrome_options.add_argument(
                "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                "(KHTML, like Gecko) Chrome/85.0.4183.121 Safari/537.36"
            )
            chrome_options.add_argument(
                "--disable-blink-features=AutomationControlled"
            )
            chrome_options.add_experimental_option(
                "excludeSwitches", ["enable-automation"]
            )
            chrome_options.add_experimental_option(
                "useAutomationExtension", False
            )
            chrome_options.add_argument("--disable-webrtc")
            chrome_options.add_argument("--profile-directory=Default")
            chrome_options.add_argument("--enable-webgl")
            chrome_options.add_argument("--ignore-gpu-blacklist")
            chrome_options.add_argument("--use-gl=swiftshader")

            driver_path = constants.CHROMEDRIVER_PATH
            logger.info("Driver path is : " + driver_path)
            driver = uc.Chrome(
                executable_path=driver_path, options=chrome_options
            )

            # driver.execute_script("Object.defineProperty(navigator, "
            #                       "'webdriver', {get: () => undefined})")
            # driver.execute_cdp_cmd('Network.setExtraHTTPHeaders', {
            #     "headers": {
            #         "Accept-Language": "en-US,en;q=0.9",
            #         "DNT": "1",  # Do Not Track
            #     }
            # })
            # try:
            #     cookie_button = driver.find_element(By.XPATH,
            #                                         '//button[text()="Accept All Cookies"]')
            #     cookie_button.click()
            #     logger.info("Cookies accepted")
            # except Exception as e:
            #     logger.error("Cookie button not found or could not be clicked:", e)

            driver.get(weblink)
            # Disable navigator.webdriver

            # Ensure the page is fully loaded
            time.sleep(constants.PDF_DOWNLOADER_WEBPAGE_LOAD_WAIT_TIME)

            # Generate PDF and store it in memory

            logger.info("Local download path is : " + local_download_path)
            pdf = driver.execute_cdp_cmd("Page.printToPDF", {"format": "A4"})

            # Save the PDF to the specified local download path
            with open(local_download_path, "wb") as file:
                file.write(base64.b64decode(pdf["data"]))

            driver.quit()

            return True
        except CustomException as exception:
            logger.error(
                f"Error generating or uploading PDF for weblink: {weblink}: {exception}"
            )
            return False
