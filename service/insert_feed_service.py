import json
import uuid

import boto3

from config import FEED_SOURCES_TO_IGNORE
from constants.api_parameters import APIParameters
from service.base_parser import BaseParser
from service.feed_service import FeedService
from utils.date_utils import str_to_datetime
from utils.entities.collection_entities import FeedUrlEntities
from utils.entities.event_entities import EntityTypes
from utils.entities.event_entities import S3Entities
from utils.entities.event_entities import ServiceEntities
from utils.entities.newsletter_entities import NewsLetterEntities
from utils.general_utils import get_domain
from utils.general_utils import publish_messages
from utils.logger import get_logger
from utils.message_format import UrlPublishMessageFormat

logger = get_logger(__name__)


class InsertNewsFeed(BaseParser):
    """Handles processing and insertion of news feed URLs,
    including validation, deduplication, and publishing."""

    def __init__(self, db_credentials):
        super().__init__(db_credentials)
        self._feed_service = FeedService()
        self.__service_id = str(uuid.uuid4())
        self.s3_client = boto3.client("s3")

    def process_urls(self, urls):
        for url in urls:
            logger.debug(f"Processing url = {url}")
            s3_bucket_name = url.get(FeedUrlEntities.S3_BUCKET_NAME.value)
            s3_object_key = url.get("s3_object_key")
            if s3_bucket_name:
                logger.info(
                    f"Fetching data from s3 bucket={s3_bucket_name} "
                    f"and key={s3_object_key}"
                )
                url = self.s3_client.get_object(
                    Bucket=s3_bucket_name, Key=s3_object_key
                )
                url = json.loads(url[S3Entities.BODY.value].read())

            entity_type = url.get(
                APIParameters.ENTITY_TYPE.value, EntityTypes.COMPANY.value
            )
            entity_id = url.get(APIParameters.COMPANY_CIN_FIELD.value, None)
            company = url.get(APIParameters.COMPANY_NAME_FIELD.value, None)
            raw_link = url.get("raw_link", None)
            news_link = url.get("news_link", None)
            image_link = url.get("image_link", None)
            published_date = url.get("published_date", None)
            source = url.get("source", None)
            title = url.get("raw_title", None)
            parser_service = url.get("parser_service", None)
            service_id = url.get("service_id", self.__service_id)
            description = url.get("raw_description", None)
            ignore_publishing = url.get("ignore_publishing", False)
            categories = url.get(FeedUrlEntities.CATEGORIES.value, None)
            tags = url.get(FeedUrlEntities.TAGS.value, None)

            if news_link and not source:
                logger.info(
                    f"Empty source, so getting source from news_link={news_link}"
                )
                source_domain = get_domain(news_link)
                source = (
                    source_domain.split(".")[0].lower().capitalize()
                    if source_domain
                    else None
                )

            required_fields = [
                news_link,
                entity_id,
                company,
                published_date,
                source,
            ]
            if entity_type == NewsLetterEntities.SECTOR.value:
                required_fields = [news_link, source]
            if not all(required_fields):
                logger.error(
                    f"Invalid input provided missing fields"
                    f" either news_link={news_link} or entity_id={entity_id} or"
                    f" company_name={company} or"
                    f" published_date={published_date}, source={source}"
                )
                continue

            if source.lower() in FEED_SOURCES_TO_IGNORE:
                logger.info(
                    f"Skipping the insertion of url for the source={source} "
                    f"as it is in ignore list: {FEED_SOURCES_TO_IGNORE}"
                )
                continue
            existing_record = self._feed_service.check_duplicate_news(
                entity_type=entity_type,
                entity_id=entity_id,
                news_link=news_link,
                news_title=title,
            )

            if existing_record:
                continue

            publishing_url = self._insert_feed_data_and_get_publish_message(
                entity_type=entity_type,
                entity_id=entity_id,
                company=company,
                link=raw_link,
                news_link=news_link,
                image_link=image_link,
                published_date=published_date,
                source=source,
                title=title,
                parser_service=parser_service,
                service_id=service_id,
                description=description,
                ignore_publishing=ignore_publishing,
                categories=categories,
                tags=tags,
            )

            if ignore_publishing is not True:
                logger.info(
                    "Publishing %s into the denoiser queue", publishing_url
                )
                message_group_id = (
                    entity_id
                    if entity_type == EntityTypes.SECTOR.value
                    else ServiceEntities.SECTOR_NEWS.value
                )
                publish_messages(
                    [publishing_url], message_group_id=message_group_id
                )
            else:
                logger.info(
                    f"Skipping publishing {publishing_url} into the denoiser queue "
                    f"as ignore_publishing = {ignore_publishing}"
                )

    def _insert_feed_data_and_get_publish_message(self, **kwargs):
        entity_type = kwargs.get("entity_type")
        entity_id = kwargs.get("entity_id")
        company = kwargs.get("company")
        link = kwargs.get("link")
        news_link = kwargs.get("news_link")
        image_link = kwargs.get("image_link")
        published_date = kwargs.get("published_date")
        source = kwargs.get("source")
        title = kwargs.get("title")
        parser_service = kwargs.get("parser_service")
        service_id = kwargs.get("service_id")
        description = kwargs.get("description")
        ignore_publishing = kwargs.get("ignore_publishing")
        categories = kwargs.get("categories")
        tags = kwargs.get("tags")
        logger.info("Inserting feeds and getting the publishing message")

        self._feed_service.insert_feed_data(
            entity_type=entity_type,
            entity_id=entity_id,
            company_name=company,
            raw_link=link,
            news_link=news_link,
            image_link=image_link,
            published_date=str_to_datetime(published_date),
            source=source,
            title=title,
            description=description,
            parser_service=parser_service,
            ignore_publishing=ignore_publishing,
            categories=categories,
            tags=tags,
        )

        message_format = UrlPublishMessageFormat(
            entity_id=entity_id,
            parser_service=parser_service,
            service_id=service_id,
            url=news_link,
            entity_type=entity_type,
        )

        publishing_url = message_format.to_response()

        return publishing_url
