from utils.general_utils import get_object_store
from utils.logger import get_logger

logger = get_logger(__name__)


class BaseParser:
    """
    BaseParser handles basic CRUD operations for SQL, NoSQL, and object storage.
    """

    def __init__(self, db_credentials):
        self._object_store = get_object_store(db_credentials)

    def insert(self, data):
        result = self._object_store.insert(data)
        return result

    def get(self, data):
        result = self._object_store.get(data)
        return result

    def update(self, data):
        result = self._object_store.upsert(data)
        return result

    # """
    # for sql insertions
    # data = {
    #     params, INSERT_SQL, service,  force=False
    #
    # }
    #
    # for s3_json_insetion
    # data = {
    #     feed_data, start_date, end_date, source, AWS_BUCKET_NAME
    # }
    #
    # for nosql_insertion
    # data = {
    #     query, collection_name, multiple_flag
    # }
    # """
    #
    # """
    # for nosql get queries
    # data = {
    #
    # }
    # """
