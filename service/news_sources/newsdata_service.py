import random
import traceback
from datetime import datetime
from urllib.parse import urlencode

import requests

import config
from config import DATE_FORMAT
from constants.api_parameters import APIParameters
from constants.api_parameters import NewsdataAPIParams
from constants.entities import FeedSources
from constants.entities import LambdaNameEntities
from service.base_parser import BaseParser
from utils.date_utils import datetime_to_str
from utils.decorators import retry_with_timeouts
from utils.entities.collection_entities import CompanyInfoEntities
from utils.entities.collection_entities import FeedUrlEntities
from utils.entities.event_entities import EntityTypes
from utils.exceptions.exceptions import NewsdataAPIException
from utils.general_utils import send_to_insert_feed_service_queue
from utils.logger import get_logger
from utils.message_format import UrlPublishMessageFormat
from utils.news_feed_utils import send_to_audit_logs
from utils.s3 import create_news_file

logger = get_logger(__name__)


class NewsdataService(BaseParser):
    """Handles interactions with the Newsdata API to retrieve news and search results."""

    def __init__(self, uuid):
        super().__init__(config.CREDENTIALS[config.NEWS_FEEDS["DATABASE"]])
        self._service_id = uuid
        newsdata_tokens_dict = config.NEWSDATA_TOKENS
        key, token = random.choice(list(newsdata_tokens_dict.items()))
        logger.info(f"Using {key} as newsdata token")
        self._random_newsdata_token = token
        self.urls_to_insert_queue = []
        self.publishing_urls = []

    @retry_with_timeouts(logger=logger, timeouts=(10, 30, 60))
    def retrieve_feed(
        self,
        news_from_date: datetime,
        news_till_date: datetime,
        company_name: str,
    ):
        newsdata_endpoint = config.NEWSDATA_ENDPOINT
        logger.info(
            f"Running the newsdata api for company={company_name}"
            f" and news_from_date={news_from_date} & news_till_date={news_till_date}"
        )
        params = {
            "apikey": self._random_newsdata_token,
            "qInTitle": company_name,
            "from_date": news_from_date,
            "to_date": news_till_date,
            "language": "en",
        }
        encoded_params = urlencode(params)
        url = f"{newsdata_endpoint}?{encoded_params}"
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            data = response.json()
        except requests.RequestException as e:
            logger.error(f"Request failed: {e}")
            raise NewsdataAPIException(f"HTTP error: {e}") from e
        except ValueError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            raise NewsdataAPIException(f"Invalid JSON: {e}") from e

        if not data or data is None:
            logger.info(f"API call failed  due to time out error "
                        f"error={data}, "
                        f"params={params}, "
                        f"error_detail={traceback.format_exc()}")
            return None

        if data.get("status") != "success":
            logger.error(f"API call failed error={data}")

            raise NewsdataAPIException(data)
        logger.info("Successfully made API call.")
        return data

    def retrieve_news_articles(
        self, news_from_date: datetime, news_till_date: datetime, name: str
    ):
        logger.info("Retrieving news articles")
        try:
            feed = self.retrieve_feed(
                news_from_date=news_from_date,
                news_till_date=news_till_date,
                company_name=name,
            )
            if not feed:
                logger.info(f"No feed retrieved={feed}")
                return []

            articles = feed.get(NewsdataAPIParams.RESULTS.value)
            count_of_articles = len(articles) if articles else 0
            articles_available = feed.get(
                NewsdataAPIParams.TOTAL_RESULTS.value
            )
            logger.info(
                f"{count_of_articles}/{articles_available} articles retrieved."
            )
            return articles
        except Exception as ex:
            logger.error("error in making API call to newsdata services")
            traceback.print_exc()
            raise ex

    def process_articles(self, cin, company_name, articles):
        logger.info("Processing fetched articles")
        if not articles:
            logger.info("No articles fetched")
            return
        for article in articles:
            content = article.get(NewsdataAPIParams.CONTENT.value)
            title = article.get(NewsdataAPIParams.TITLE.value)
            categories = article.get(NewsdataAPIParams.CATEGORY.value)
            article_image = article.get(NewsdataAPIParams.IMAGE_URL.value)
            countries = article.get(NewsdataAPIParams.COUNTRY.value)
            source = article.get(NewsdataAPIParams.SOURCE_NAME.value)
            news_link = article.get(NewsdataAPIParams.LINK.value)
            published_date = article.get(
                NewsdataAPIParams.PUBLISHED_DATE.value
            )
            entity_type = EntityTypes.COMPANY.value
            s3_message = {
                FeedUrlEntities.CIN.value: cin,
                FeedUrlEntities.ENTITY_TYPE.value: entity_type,
                FeedUrlEntities.NAME.value: company_name,
                FeedUrlEntities.RAW_LINK.value: news_link,
                FeedUrlEntities.NEWS_LINK.value: news_link,
                FeedUrlEntities.PUBLISHED_DATE.value: published_date,
                FeedUrlEntities.SOURCE.value: source,
                FeedUrlEntities.RAW_TITLE.value: title,
                FeedUrlEntities.PARSER_SERVICE.value: (
                    LambdaNameEntities.GET_NEWSDATA_NEWS_FEED.value
                ),
                FeedUrlEntities.SERVICE_ID.value: self._service_id,
                FeedUrlEntities.RAW_DESCRIPTION.value: content,
                FeedUrlEntities.IGNORE_PUBLISHING.value: False,
                FeedUrlEntities.CATEGORIES.value: categories,
                FeedUrlEntities.IMAGE_LINK.value: article_image,
                FeedUrlEntities.COUNTRIES.value: countries,
            }
            message_data = create_news_file(s3_message, entity_type, cin)
            self.urls_to_insert_queue.append(message_data)
            message_format = UrlPublishMessageFormat(
                cin=cin,
                parser_service=FeedSources.NEWSDATA.value,
                service_id=self._service_id,
                url=news_link,
                entity_type=CompanyInfoEntities.COMPANY.value,
            )
            publishing_url = message_format.to_response()
            self.publishing_urls.append(publishing_url)

    def newsdata_worker(self, **kwargs):
        cin = kwargs.get(APIParameters.COMPANY_CIN_FIELD.value)
        name = kwargs.get(APIParameters.COMPANY_NAME_FIELD.value)
        news_from_date = kwargs.get(
            APIParameters.NEWS_FROM_DATE.value
        )  # datetime object
        news_till_date = kwargs.get(APIParameters.NEWS_TILL_DATE.value)
        news_from_date = datetime_to_str(news_from_date, DATE_FORMAT)
        news_till_date = datetime_to_str(news_till_date, DATE_FORMAT)
        articles = self.retrieve_news_articles(
            news_from_date=news_from_date,
            news_till_date=news_till_date,
            name=name,
        )
        if not articles:
            logger.info(f"No articles retrieved for cin={cin}")
            return

        logger.info(f"Initiating newsdata worker for cin={cin}")
        self.process_articles(cin, name, articles)
        feed_available = (len(articles) if articles else 0) > 0
        messages_published_count = len(self.publishing_urls)
        send_to_insert_feed_service_queue(urls=self.urls_to_insert_queue)
        send_to_audit_logs(
            service_id=self._service_id,
            company_cin=cin,
            start_date=news_from_date,
            end_date=news_till_date,
            parser_flagged=False,
            feeds_available=feed_available,
            messages_published=messages_published_count,
            publishing_urls=self.publishing_urls,
        )
        return self.publishing_urls
