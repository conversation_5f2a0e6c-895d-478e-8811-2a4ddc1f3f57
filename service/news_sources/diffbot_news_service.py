import http
import random
import traceback

import requests

import config
from constants.api_parameters import APIParameters
from constants.api_parameters import DiffbotAPIParams
from constants.entities import FeedSources
from constants.entities import LambdaNameEntities
from service.base_parser import BaseParser
from utils.date_utils import datetime_to_str
from utils.date_utils import timestamp_to_datetime
from utils.entities.collection_entities import CompanyInfoEntities
from utils.entities.collection_entities import FeedUrlEntities
from utils.entities.event_entities import EntityTypes
from utils.exceptions.exceptions import DiffbotURINotPresent
from utils.general_utils import send_to_insert_feed_service_queue
from utils.logger import get_logger
from utils.message_format import UrlPublishMessageFormat
from utils.news_feed_utils import send_to_audit_logs
from utils.s3 import create_news_file

logger = get_logger(__name__)


def get_diffbot_payload(news_from_date, diffbot_entity_uri):
    query = f'type:Article tags.uri:"{diffbot_entity_uri}" date>={news_from_date} language:"en"'
    payload = {
        "type": "query",
        "from": 0,
        "size": config.MAX_DIFFBOT_ARTICLES_TO_FETCH,
        "format": "json",
        "nonCanonicalFacts": False,
        "noDedupArticles": False,
        "report": "false",
        "query": query,
        "cluster": "best",
    }
    return payload


class DiffbotService(BaseParser):
    """Handles interactions with the Diffbot API to retrieve news and search results."""

    def __init__(self, uuid):
        super().__init__(config.CREDENTIALS[config.NEWS_FEEDS["DATABASE"]])
        self._service_id = uuid
        diffbot_tokens_dict = config.DIFFBOT_TOKENS
        key, token = random.choice(list(diffbot_tokens_dict.items()))
        logger.info(f"Using {key} as diffbot token")
        self._random_diffbot_token = token
        self._diffbot_url = config.DIFFBOT_URL + self._random_diffbot_token
        self.urls_to_insert_queue = []
        self.publishing_urls = []

    def retrieve_feed(self, news_from_date, diffbot_entity_uri):
        payload = get_diffbot_payload(news_from_date, diffbot_entity_uri)
        logger.info(f"Running the diffbot api for payload={payload}")
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
        }
        response = requests.post(
            self._diffbot_url, json=payload, headers=headers, timeout=20
        )
        if response.status_code in [
            http.HTTPStatus.TOO_MANY_REQUESTS,
            http.HTTPStatus.INTERNAL_SERVER_ERROR,
        ]:
            logger.error(
                f"API call with payload={payload} failed"
                f" with status_code={response.status_code}"
            )
        response.raise_for_status()
        logger.info("Successfully made API call.")
        return response.json()

    def retrieve_news_articles(self, news_from_date, diffbot_entity_uri):
        logger.info("Retrieving news articles")
        try:
            feed = self.retrieve_feed(news_from_date, diffbot_entity_uri)
            if not feed:
                logger.info(f"No feed retrieved={feed}")
                return []
            articles = feed.get(APIParameters.DATA.value)
            count_of_articles = len(articles) if articles else 0
            logger.info(f"{count_of_articles} articles retrieved.")
            if count_of_articles == config.MAX_DIFFBOT_ARTICLES_TO_FETCH:
                logger.info(
                    f"Maximum number of articles ({config.MAX_DIFFBOT_ARTICLES_TO_FETCH})"
                    f" have been fetched. There may be more articles available."
                )
            return articles
        except Exception as ex:
            logger.error("error in making API call to diffbot services")
            traceback.print_exc()
            raise ex

    def process_articles(self, cin, company_name, articles):
        logger.info("Processing fetched articles")
        if not articles:
            logger.info("No articles fetched")
            return
        for article in articles:
            article_data = article.get(DiffbotAPIParams.ENTITY.value)
            title = article_data.get(DiffbotAPIParams.TITLE.value)
            article_text = article_data.get(DiffbotAPIParams.TEXT.value)
            categories = article_data.get(DiffbotAPIParams.CATEGORIES.value)
            article_image = article_data.get(DiffbotAPIParams.IMAGE.value)
            tags = article_data.get(DiffbotAPIParams.TAGS.value)
            source = article_data.get(DiffbotAPIParams.SITE_NAME.value)
            news_link = article_data.get(
                DiffbotAPIParams.RESOLVED_PAGE_URL.value
            )
            published_date_timestamp = article_data.get(
                DiffbotAPIParams.DATE.value, {}
            ).get(DiffbotAPIParams.TIMESTAMP.value, 0)
            published_date = timestamp_to_datetime(published_date_timestamp)
            published_date_str = datetime_to_str(
                timestamp=published_date,
                date_format="%Y-%m-%d %H:%M:%S",
            )

            if not news_link:
                news_link = article_data.get(DiffbotAPIParams.PAGE_URL.value)

            category_names = []
            tag_names = []
            if categories:
                for category in categories:
                    category_names.append(
                        category[DiffbotAPIParams.NAME.value]
                    )
            if tags:
                for tag in tags:
                    tag_names.append(tag[DiffbotAPIParams.LABEL.value])
            entity_type = EntityTypes.COMPANY.value
            s3_message = {
                FeedUrlEntities.CIN.value: cin,
                FeedUrlEntities.ENTITY_TYPE.value: entity_type,
                FeedUrlEntities.NAME.value: company_name,
                FeedUrlEntities.RAW_LINK.value: news_link,
                FeedUrlEntities.NEWS_LINK.value: news_link,
                FeedUrlEntities.PUBLISHED_DATE.value: published_date_str,
                FeedUrlEntities.SOURCE.value: source,
                FeedUrlEntities.RAW_TITLE.value: title,
                FeedUrlEntities.PARSER_SERVICE.value: (
                    LambdaNameEntities.GET_DIFFBOT_NEWS_FEED.value
                ),
                FeedUrlEntities.SERVICE_ID.value: self._service_id,
                FeedUrlEntities.RAW_DESCRIPTION.value: article_text,
                FeedUrlEntities.IGNORE_PUBLISHING.value: False,
                FeedUrlEntities.CATEGORIES.value: category_names,
                FeedUrlEntities.IMAGE_LINK.value: article_image,
                FeedUrlEntities.TAGS.value: tag_names,
            }
            message_data = create_news_file(s3_message, entity_type, cin)
            self.urls_to_insert_queue.append(message_data)
            message_format = UrlPublishMessageFormat(
                cin=cin,
                parser_service=FeedSources.DIFFBOT_NEWS.value,
                service_id=self._service_id,
                url=news_link,
                entity_type=CompanyInfoEntities.COMPANY.value,
            )
            publishing_url = message_format.to_response()
            self.publishing_urls.append(publishing_url)

    def diffbot_worker(self, **kwargs):
        cin = kwargs.get(APIParameters.COMPANY_CIN_FIELD.value)
        name = kwargs.get(APIParameters.COMPANY_NAME_FIELD.value)
        news_from_date = kwargs.get(APIParameters.NEWS_FROM_DATE.value)
        news_till_date = kwargs.get(APIParameters.NEWS_TILL_DATE.value)
        diffbot_entity_uri = kwargs.get(APIParameters.DIFFBOT_ENTITY_URI.value)
        if diffbot_entity_uri:
            articles = self.retrieve_news_articles(
                news_from_date.timestamp(), diffbot_entity_uri
            )
        else:
            logger.error(f"No diffbot URI present for cin={cin}")
            raise DiffbotURINotPresent("Diffbot URI not present")
        logger.info(f"Initiating diffbot worker for cin={cin}")
        self.process_articles(cin, name, articles)
        feed_available = (len(articles) if articles else 0) > 0
        messages_published_count = len(self.publishing_urls)
        send_to_insert_feed_service_queue(urls=self.urls_to_insert_queue)
        send_to_audit_logs(
            service_id=self._service_id,
            company_cin=cin,
            start_date=news_from_date,
            end_date=news_till_date,
            parser_flagged=False,
            feeds_available=feed_available,
            messages_published=messages_published_count,
            publishing_urls=self.publishing_urls,
        )
        return self.publishing_urls
