import uuid
from datetime import date
from datetime import datetime

from config import CREDENTI<PERSON><PERSON>
from config import NEWS_FEEDS
from service.base_parser import BaseParser
from service.company_service import CompanyService
from service.feed_service import FeedService
from service.mail.gmail_service import GmailService
from utils.date_utils import current_datetime
from utils.date_utils import datetime_to_str
from utils.entities.collection_entities import CompanyInfoEntities
from utils.entities.collection_entities import FeedParserJobStatusEntities
from utils.entities.collection_entities import FeedUrlEntities
from utils.entities.gmail_entities import SearchCriteriaEntities
from utils.entities.newsletter_entities import NewsLetterEmailResponseEntities
from utils.entities.newsletter_entities import NewsLetterEntities
from utils.entities.newsletter_entities import NewsletterInputEntities
from utils.entities.newsletter_entities import NewsletterNameEntities
from utils.entities.newsletter_entities import NewsletterSourceEntities
from utils.entities.parse_entities import ParserServiceEntities
from utils.general_utils import get_og_tags
from utils.general_utils import get_redirect_url
from utils.general_utils import ner_api
from utils.general_utils import send_to_insert_feed_service_queue
from utils.html_parser import extract_arc_newsletter
from utils.html_parser import extract_inc42_newsletter
from utils.logger import get_logger

logger = get_logger(__name__)


class NewsletterService(BaseParser):
    """
    Service for managing newsletters, including parsing,
    mapping to companies, and inserting into the feed service.
    """

    def __init__(self):
        super().__init__(db_credentials=CREDENTIALS[NEWS_FEEDS["DATABASE"]])
        self._feed_service = FeedService()
        self._company_service = CompanyService()
        self.__parser_service_name = ParserServiceEntities.NEWSLETTER.value

    def _get_parser_service_job_name(
        self, news_source: str, newsletter_name: str
    ):
        return f"{self.__parser_service_name}_{news_source}_{newsletter_name}".lower()

    def update_newsletter_configs_with_last_run_details(
        self, newsletter_configs
    ):
        logger.info(
            f"Updating the newsletter configs with last "
            f"run details with newsletter_config={newsletter_configs}"
        )
        updated_newsletter_configs = []
        for newsletter_config in newsletter_configs:
            parser_job_name = self._get_parser_service_job_name(
                news_source=newsletter_config.get(
                    NewsletterInputEntities.NEWSLETTER_SOURCE.value
                ),
                newsletter_name=newsletter_config.get(
                    NewsletterInputEntities.NEWSLETTER_NAME.value
                ),
            )

            job_state_detail = self._feed_service.get_last_job_state(
                parser_service=parser_job_name, company_cin=None
            )
            if job_state_detail:
                search_criteria = newsletter_config.get(
                    NewsletterInputEntities.NEWSLETTER_SEARCH_CRITERIA.value
                )
                search_criteria.update(
                    {
                        SearchCriteriaEntities.FROM_DATE.value: job_state_detail.get(
                            FeedParserJobStatusEntities.LAST_RUN_END_DATE.value
                        ).strftime(
                            "%d-%b-%Y"
                        )
                    }
                )
                newsletter_config.update(
                    {
                        NewsletterInputEntities.NEWSLETTER_SEARCH_CRITERIA.value: search_criteria
                    }
                )

            updated_newsletter_configs.append(newsletter_config)

        return updated_newsletter_configs

    def update_newsletter_job_state(
        self, newsletter_configs: list, start_date: date, end_date: date
    ):
        logger.info("Update the newsletter job state")
        for newsletter_config in newsletter_configs:
            parser_job_name = self._get_parser_service_job_name(
                news_source=newsletter_config.get(
                    NewsletterInputEntities.NEWSLETTER_SOURCE.value
                ),
                newsletter_name=newsletter_config.get(
                    NewsletterInputEntities.NEWSLETTER_NAME.value
                ),
            )
            self._feed_service.upsert_job_state(
                company_cin=None,
                parser_service=parser_job_name,
                start_date=start_date,
                end_date=end_date,
            )

    def parse_newsletter_email_body(self, email_data_list: list):
        logger.info("Parsing the newsletter email body")
        newsletters = []

        for email_data in email_data_list:
            logger.info(
                f"Parsing the email body for the "
                f"email_id={email_data.get(NewsLetterEmailResponseEntities.EMAIL_ID.value)}"
            )
            extracted_newsletters = []
            newsletter_name = email_data.get(
                NewsLetterEmailResponseEntities.NEWSLETTER_NAME.value
            )
            newsletter_body = email_data.get(
                NewsLetterEmailResponseEntities.BODY.value
            )
            newsletter_date_str = email_data.get(
                NewsLetterEmailResponseEntities.DATE.value
            )

            if (
                newsletter_name.lower()
                == NewsletterNameEntities.INC42.value.lower()
            ):
                extracted_newsletters = extract_inc42_newsletter(
                    html_body=newsletter_body
                )
            elif (
                newsletter_name.lower()
                == NewsletterNameEntities.ARC.value.lower()
            ):
                extracted_newsletters = extract_arc_newsletter(
                    html_body=newsletter_body
                )
            else:
                logger.error(
                    f"Invalid newsletter name={newsletter_name} provided in the config"
                )

            if extracted_newsletters:
                for extracted_newsletter in extracted_newsletters:
                    extracted_newsletter.update(
                        {
                            NewsLetterEntities.PUBLISHED_DATE.value: newsletter_date_str
                        }
                    )

                newsletters.extend(extracted_newsletters)

        return newsletters

    @staticmethod
    def convert_urls_to_permanent_url(newsletters):
        logger.info(
            f"Converting urls to permanent url for these url ={newsletters}"
        )
        updated_newsletters = []
        for newsletter in newsletters:
            news_link = newsletter.get(NewsLetterEntities.NEWS_LINK.value)
            news_image = newsletter.get(NewsLetterEntities.NEWS_IMAGE.value)

            if news_link:
                newsletter.update(
                    {
                        NewsLetterEntities.NEWS_LINK.value: get_redirect_url(
                            news_link
                        )
                    }
                )

            if not news_image:
                parsed_og_tags = get_og_tags(
                    url=newsletter.get(NewsLetterEntities.NEWS_LINK.value)
                )
                news_image = parsed_og_tags.get("og:image")

            if news_image:
                newsletter.update(
                    {
                        NewsLetterEntities.NEWS_IMAGE.value: get_redirect_url(
                            news_image
                        )
                    }
                )

            updated_newsletters.append(newsletter)

        return updated_newsletters

    def get_newsletter(self, newsletter_configs):
        logger.info(
            f"Getting the newsletter for the newsletter configs={newsletter_configs}"
        )
        email_data_list = []
        for newsletter_config in newsletter_configs:
            newsletter_source = newsletter_config.get(
                NewsletterInputEntities.NEWSLETTER_SOURCE.value
            )
            newsletter_name = newsletter_config.get(
                NewsletterInputEntities.NEWSLETTER_NAME.value
            )
            newsletter_email = newsletter_config.get(
                NewsletterInputEntities.NEWSLETTER_EMAIL.value
            )
            newsletter_password = newsletter_config.get(
                NewsletterInputEntities.NEWSLETTER_PASSWORD.value
            )
            newsletter_date_format = newsletter_config.get(
                NewsletterInputEntities.NEWSLETTER_DATE_FORMAT.value
            )
            newsletter_search_criteria = newsletter_config.get(
                NewsletterInputEntities.NEWSLETTER_SEARCH_CRITERIA.value
            )

            if (
                not newsletter_source
                or not newsletter_name
                or not newsletter_email
                or not newsletter_password
            ):
                logger.error(
                    f"Either newsletter_source={newsletter_source} or "
                    f"newsletter_name={newsletter_name} "
                    f"or newsletter_email={newsletter_email} or "
                    f"newsletter_password is empty"
                )
                continue

            if NewsletterSourceEntities.GMAIL.value == newsletter_source:
                gmail_service = GmailService(
                    username=newsletter_email, password=newsletter_password
                )
                gmail_service.select_mailbox(mailbox="INBOX")
                gmail_data_list = gmail_service.get_emails(
                    search_criteria=newsletter_search_criteria,
                    text_format=False,
                    newsletter_date_format=newsletter_date_format,
                )
                # [
                #     gmail_data.update(
                #         {
                #             NewsLetterEmailResponseEntities.NEWSLETTER_NAME.value: newsletter_name
                #         }
                #     )
                #     for gmail_data in gmail_data_list
                # ]
                for gmail_data in gmail_data_list:
                    gmail_data.update(
                        {
                            NewsLetterEmailResponseEntities.NEWSLETTER_NAME.value: newsletter_name
                        }
                    )

                email_data_list.extend(gmail_data_list)

        logger.info(
            f"Total email data list before parsing={len(email_data_list)}"
        )
        newsletters = self.parse_newsletter_email_body(
            email_data_list=email_data_list
        )
        logger.info(f"Total news found from email={len(newsletters)}")
        return NewsletterService.convert_urls_to_permanent_url(
            newsletters=newsletters
        )

    def add_new_company(self, company_name: str):
        logger.info(
            f"Adding a new company to the collection with name={company_name}"
        )
        company_detail = self._company_service.create_company(
            company_name=company_name
        )
        return company_detail

    def map_newsletters_to_company(self, newsletters: list):
        newsletters_with_cin = []
        logger.info("Map newsletters to company")

        for newsletter in newsletters:

            newsletter_title = newsletter.get(NewsLetterEntities.TITLE.value)

            org_entities = ner_api(text=newsletter_title)
            org_entities = [
                org_entity.get("word")
                for org_entity in org_entities
                if org_entity.get("entity") == "Organization"
            ]
            org_entities = [org_entity.lower() for org_entity in org_entities]
            companies_detail = []
            for org_entity in org_entities:
                details = self._company_service.get_company_details(
                    name=org_entity
                )
                if len(details) == 0:
                    details.append(
                        {
                            CompanyInfoEntities.CIN.value: uuid.uuid4().hex,
                            CompanyInfoEntities.NAME.value: org_entity,
                            FeedUrlEntities.IGNORE_PUBLISHING.value: True,
                            CompanyInfoEntities.IS_ACTIVE.value: False,
                        }
                    )

                companies_detail.extend(details)

            for detail in companies_detail:
                company_cin = {
                    NewsLetterEntities.CIN.value: detail.get(
                        CompanyInfoEntities.CIN.value
                    ),
                    NewsLetterEntities.COMPANY_NAME.value: detail.get(
                        CompanyInfoEntities.NAME.value
                    ),
                    FeedUrlEntities.IGNORE_PUBLISHING.value: detail.get(
                        FeedUrlEntities.IGNORE_PUBLISHING.value,
                        detail.get(CompanyInfoEntities.IS_ACTIVE.value, False),
                    ),
                }

                newsletters_with_cin.append({**newsletter, **company_cin})
        return newsletters_with_cin

    def ignore_duplicate_newsletters(self, newsletters):
        logger.info("Ignoring the duplicate newsletters")
        duplicate_removed_newsletters = []
        for newsletter in newsletters:
            company_cin = newsletter.get(NewsLetterEntities.CIN.value)
            news_link = newsletter.get(NewsLetterEntities.NEWS_LINK.value)
            if not self._feed_service.check_duplicate_news(
                company_cin=company_cin,
                news_link=news_link,
                news_title=newsletter.get(NewsLetterEntities.TITLE.value),
            ):
                duplicate_removed_newsletters.append(newsletter)
            else:
                logger.info(
                    f"We found that the news_link={news_link} for the company_cin={company_cin}"
                )

        return duplicate_removed_newsletters

    def insert_newsletters(self, newsletters: list):
        logger.info(f"Inserting newsletter with {len(newsletters)}")
        urls_to_insert_queue = []
        for newsletter in newsletters:
            company_cin = newsletter.get(NewsLetterEntities.CIN.value)
            news_link = newsletter.get(NewsLetterEntities.NEWS_LINK.value)
            published_date = newsletter.get(
                NewsLetterEntities.PUBLISHED_DATE.value
            )

            if isinstance(published_date, datetime):
                published_date = datetime_to_str(
                    timestamp=published_date, date_format="%Y-%m-%d %H:%M:%S"
                )
            message_body = {
                FeedUrlEntities.CIN.value: company_cin,
                FeedUrlEntities.NAME.value: newsletter.get(
                    NewsLetterEntities.COMPANY_NAME.value
                ),
                FeedUrlEntities.RAW_LINK.value: news_link,
                FeedUrlEntities.NEWS_LINK.value: news_link,
                FeedUrlEntities.IMAGE_LINK.value: newsletter.get(
                    NewsLetterEntities.NEWS_IMAGE.value
                ),
                FeedUrlEntities.PUBLISHED_DATE.value: published_date,
                FeedUrlEntities.SOURCE.value: newsletter.get(
                    NewsLetterEntities.NEWS_SOURCE.value
                ),
                FeedUrlEntities.RAW_TITLE.value: newsletter.get(
                    NewsLetterEntities.TITLE.value
                ),
                FeedUrlEntities.RAW_DESCRIPTION.value: newsletter.get(
                    NewsLetterEntities.DESCRIPTION.value
                ),
                FeedUrlEntities.PARSER_SERVICE.value: self.__parser_service_name,
                FeedUrlEntities.IGNORE_PUBLISHING.value: newsletter.get(
                    FeedUrlEntities.IGNORE_PUBLISHING.value
                ),
            }
            urls_to_insert_queue.append(message_body)

        send_to_insert_feed_service_queue(urls=urls_to_insert_queue)

    def run_newsletter(self, newsletter_configs: list):
        """
        1.Get last run config
        2.Get new emails based on config
        3.Map newsletter to company
        4. Ignore duplicate newsletter
        5. Create newsletters
        6. Publish newsletters
        :param newsletter_configs:
        :return:
        """
        logger.info(
            f"Running the newsletter for the configs={newsletter_configs}"
        )
        start_date = current_datetime()

        newsletter_configs = (
            self.update_newsletter_configs_with_last_run_details(
                newsletter_configs=newsletter_configs
            )
        )
        newsletters = self.get_newsletter(
            newsletter_configs=newsletter_configs
        )
        company_newsletters = self.map_newsletters_to_company(
            newsletters=newsletters
        )

        duplicate_removed_newsletters = self.ignore_duplicate_newsletters(
            newsletters=company_newsletters
        )
        self.insert_newsletters(newsletters=duplicate_removed_newsletters)

        self.update_newsletter_job_state(
            newsletter_configs=newsletter_configs,
            start_date=start_date,
            end_date=current_datetime(),
        )
