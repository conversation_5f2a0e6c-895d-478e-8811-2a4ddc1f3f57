import json

import boto3

from utils.exceptions.exceptions import CustomException
from utils.logger import get_logger

logger = get_logger(__name__)


class S3Store:
    """
    Service for storing feed data in AWS S3 by creating folders and JSON files with logging support.
    """

    connection = None

    def __init__(self, database):
        self._aws_bucket_name = database["NAME"]
        self._s3 = boto3.client("s3")

    def _create_s3_content(self, **kwargs):
        company = kwargs.get("company")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        source = kwargs.get("source")
        feeds = kwargs.get("feeds")
        folder_name = f"{company}"
        file_name = f"{start_date}_to_{end_date}_{source}" + ".json"
        file_contents = feeds
        return folder_name, file_name, file_contents

    def _create_s3_file(self, folder_name, file_name, file_contents):
        self._s3.put_object(
            Bucket=self._aws_bucket_name,
            Key=f"{folder_name}/{file_name}",
            Body=json.dumps(file_contents),
        )

    def _create_s3_folder(self, folder_name):
        self._s3.put_object(
            Bucket=self._aws_bucket_name, Key=f"{folder_name}/", Body=""
        )

    def insert_data(self, feed_data, start_date, end_date, source):
        try:
            for company, feeds in feed_data.items():
                if len(feeds) == 0:
                    continue
                (folder_name, file_name, file_contents) = (
                    self._create_s3_content(
                        company=company,
                        start_date=start_date,
                        end_date=end_date,
                        source=source,
                        feeds=feeds,
                    )
                )
                response = self._s3.list_objects_v2(
                    Bucket=self._aws_bucket_name, Prefix=folder_name
                )
                if "Contents" in response:
                    self._create_s3_file(folder_name, file_name, file_contents)
                    logger.info(f"{folder_name} s3 appended")
                else:
                    self._create_s3_folder(folder_name)
                    self._create_s3_file(folder_name, file_name, file_contents)
                    logger.info(f"{folder_name} s3 created")
        except CustomException as err:
            logger.error(f"error: {repr(err)}")
