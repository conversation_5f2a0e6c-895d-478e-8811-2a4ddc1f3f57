import sys

from pymongo import MongoClient

from constants.entities import CredentialConstants
from utils.logger import get_logger

sys.path.append("/opt")
logger = get_logger(__name__)


class NoSQLStore:
    """A class for interacting with MongoDB for CRUD operations."""

    connection = None

    def __init__(self, database):
        client = MongoClient(
            database["DOCUMENT_DB_URI"],
            connectTimeoutMS=database.get(
                CredentialConstants.CONN_TIMEOUT.value
            ),
            serverSelectionTimeoutMS=database.get(
                CredentialConstants.CONN_TIMEOUT.value
            ),
        )
        self._client = client
        self._db = client[database["DATABASE"]]

    def _get_collection(self, collection):
        collection = self._db[collection]
        return collection

    def _insert_one(self, document, collection_name):
        collection = self._get_collection(collection=collection_name)
        result = collection.insert_one(document)
        return result

    def _insert_many(self, documents, collection_name):
        collection = self._get_collection(collection=collection_name)
        result = collection.insert_many(documents)
        return result

    def _find_one(self, query, collection_name, projection, sort_order):
        collection = self._get_collection(collection=collection_name)
        result = collection.find_one(
            query, projection=projection, sort=sort_order
        )
        return result

    def _find_all(self, **kwargs):
        collection_name = kwargs.get("collection_name")
        query = kwargs.get("query")
        projection = kwargs.get("projection")
        sort_order = kwargs.get("sort_order")
        limit = kwargs.get("limit")
        collection = self._get_collection(collection=collection_name)
        results = collection.find(
            query, projection=projection, sort=sort_order
        ).limit(limit)
        return results

    def _insert_request_parametres(self, data):
        multiple_flag = False
        document = data["document"]
        collection_name = data["collection_name"]
        if data.get("multiple_flag") is not None:
            multiple_flag = data["multiple_flag"]
        return document, collection_name, multiple_flag

    def insert(self, data):
        (document, collection_name, multiple_flag) = (
            self._insert_request_parametres(data)
        )
        result = None
        if multiple_flag is False:
            result = self._insert_one(document, collection_name)
        else:
            result = self._insert_many(
                documents=document, collection_name=collection_name
            )
        return result

    def _get_request_parameters(self, data):
        query = {}
        collection_name = None
        projection = {"_id": 0}
        sort_order = None
        multiple_flag = False
        limit = 1000000
        if data.get("query") is not None:
            query = data["query"]
        if data.get("collection_name") is not None:
            collection_name = data["collection_name"]
        if data.get("projection") is not None:
            projection = data["projection"]
        if data.get("sort_order") is not None:
            sort_order = data["sort_order"]
        if data.get("multiple_flag") is not None:
            multiple_flag = data["multiple_flag"]
        if data.get("limit") is not None:
            limit = data["limit"]
        return (
            query,
            collection_name,
            projection,
            sort_order,
            multiple_flag,
            limit,
        )

    def _get_upsert_parameters(self, data):
        filter_criteria = data["filter_criteria"]
        update_operation = data["update_operation"]
        collection_name = data["collection_name"]
        return filter_criteria, update_operation, collection_name

    def upsert(self, data):
        (filter_criteria, update_operation, collection_name) = (
            self._get_upsert_parameters(data)
        )
        collection = self._get_collection(collection=collection_name)
        result = collection.update_one(
            filter_criteria, update_operation, upsert=True
        )
        return result

    def get(self, data):
        (
            query,
            collection_name,
            projection,
            sort_order,
            multiple_flag,
            limit,
        ) = self._get_request_parameters(data)
        result = None
        if multiple_flag is False:
            result = self._find_one(
                query, collection_name, projection, sort_order
            )
        else:
            result = self._find_all(
                collection_name=collection_name,
                query=query,
                projection=projection,
                sort_order=sort_order,
                limit=limit,
            )
        return result
