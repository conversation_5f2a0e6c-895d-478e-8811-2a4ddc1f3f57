import re
import uuid

from config import CREDENTIALS
from config import NEWS_FEEDS
from constants.api_parameters import APIParameters
from service.base_parser import BaseParser
from service.company_service_response import get_company_details_response
from utils.entities.collection_entities import CompanyInfoEntities
from utils.logger import get_logger

logger = get_logger(__name__)


class CompanyService(BaseParser):
    """
    Service for managing company data, including creating new
    companies and retrieving company details.
    """

    def __init__(self):
        super().__init__(db_credentials=CREDENTIALS[NEWS_FEEDS["DATABASE"]])
        self.__company_info_collection = (
            APIParameters.COMPANY_INFO_COLLECTION.value
        )

    def create_company(self, company_name):
        logger.info(f"Create company={company_name}")
        company_cin = uuid.uuid4().hex
        insert_ack = self.insert(
            data={
                "document": {
                    CompanyInfoEntities.CIN.value: company_cin,
                    CompanyInfoEntities.COMPANY.value: {
                        CompanyInfoEntities.ALIAS_NAME.value: [
                            company_name.lower(),
                            company_name.upper(),
                            company_name,
                        ]
                    },
                    CompanyInfoEntities.NAME.value: company_name,
                    CompanyInfoEntities.IS_ACTIVE.value: False,
                    CompanyInfoEntities.IS_READY.value: False,
                },
                "collection_name": self.__company_info_collection,
            }
        )
        company_id = str(insert_ack.inserted_id)
        logger.info(f"New company created with company_id={company_id}")

        return {
            CompanyInfoEntities.CIN.value: company_cin,
            CompanyInfoEntities.NAME.value: company_name,
            CompanyInfoEntities.IS_ACTIVE.value: False,
            CompanyInfoEntities.IS_READY.value: False,
        }

    def get_company_details(self, name: str):
        logger.info(f"get company details for the name={name}")

        escaped_name = re.escape(name)
        companies = self.get(
            data={
                "query": {
                    "$or": [
                        {
                            f"{CompanyInfoEntities.NAME.value}": re.compile(
                                f"^{escaped_name}$", re.IGNORECASE
                            )
                        },
                        {
                            f"{CompanyInfoEntities.COMPANY.value}."
                            f"{CompanyInfoEntities.NAME_FOR_NEWS.value}": {
                                "$in": [
                                    re.compile(
                                        f"^{escaped_name}$", re.IGNORECASE
                                    )
                                ]
                            }
                        },
                        {
                            f"{CompanyInfoEntities.COMPANY.value}."
                            f"{CompanyInfoEntities.ALIAS_NAME.value}": {
                                "$in": [name]
                            }
                        },
                    ],
                    CompanyInfoEntities.CIN.value: {"$exists": True},
                },
                "collection_name": APIParameters.COMPANY_INFO_COLLECTION.value,
                "multiple_flag": True,
                "projection": {
                    "_id": 0,
                    CompanyInfoEntities.CIN.value: 1,
                    CompanyInfoEntities.NAME.value: 1,
                    CompanyInfoEntities.IS_ACTIVE.value: 1,
                    CompanyInfoEntities.IS_READY.value: 1,
                },
            }
        )

        return get_company_details_response(companies)
