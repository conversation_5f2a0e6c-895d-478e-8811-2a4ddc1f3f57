import json

from config import WEBLINK_CONVERTED_PDF_QUEUE_URL
from constants.constants import LOCAL_FILE_DOWNLOAD_PREFIX
from DTOs.pdf_downloader_response_dto import PDFDownloadResponseDTO
from service.weblink_processor_service import WeblinkProcessor
from utils.exceptions.feed_parser_base_exceptions import (
    WeblinkToPDFConversionError,
)
from utils.general import get_uuid
from utils.logger import get_logger
from utils.payload import get_pdf_download_request_payload
from utils.s3 import upload_file
from utils.sqs import SqsService

logger = get_logger(__name__)


class WeblinkToPdfOrchestratorService:
    """Orchestrates the conversion of weblinks to PDF and uploads them."""

    def __init__(
        self,
    ):
        self.weblink_processor = WeblinkProcessor()

    def convert_weblink_and_upload(self, **kwargs):
        """
        required fields
        weblink
        bucket_name: name of the bucket we want to upload
        title: tile of the file
        file_storage_path: storage location in s3
        """
        weblink = kwargs.get("weblink")
        bucket_name = kwargs.get("bucket_name")
        title = kwargs.get("title")
        file_storage_path = kwargs.get("file_storage_path")
        theme_ids = kwargs.get("theme_ids")
        message_trace_id = kwargs.get("message_trace_id")
        org_id = kwargs.get("org_id")
        user_id = kwargs.get("user_id")
        document_type = kwargs.get("document_type")

        if not weblink:
            raise ValueError("weblink not found")
        if not bucket_name:
            raise ValueError("bucket name not found")
        if not title:
            raise ValueError("title not found")
        if not file_storage_path:
            raise ValueError("file_storage_path not found")

        try:
            local_download_path = f"{LOCAL_FILE_DOWNLOAD_PREFIX}{title}.pdf"
            processed_successfully = (
                self.weblink_processor.process_weblink_to_pdf(
                    weblink, local_download_path
                )
            )
        except Exception as exception:
            logger.error(
                f"Error generating or uploading PDF for title: "
                f"{title} and weblink: {weblink}: {exception}"
            )
            raise WeblinkToPDFConversionError(
                f"Error generating or uploading PDF for title: {title} and weblink: {weblink}"
            ) from exception

        if processed_successfully:
            upload_file(
                file_path=local_download_path,
                bucket_name=bucket_name,
                object_key=file_storage_path,
            )

            pdf_download_response_dto = PDFDownloadResponseDTO(
                theme_ids=theme_ids,
                message_trace_id=message_trace_id,
                bucket=bucket_name,
                object_link=file_storage_path,
                title=title,
                raw_link=weblink,
                org_id=org_id,
                user_id=user_id,
                document_type=document_type,
            )

            SqsService.send_message_to_queue(
                message=json.dumps(
                    get_pdf_download_request_payload(pdf_download_response_dto)
                ),
                queue_url=WEBLINK_CONVERTED_PDF_QUEUE_URL,
                message_group_id=get_uuid(),
            )
