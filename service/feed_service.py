from datetime import date
from datetime import datetime

from config import CREDENTIALS
from config import NEWS_FEEDS
from constants.api_parameters import APIParameters
from service.base_parser import BaseParser
from utils.entities.collection_entities import FeedParserJobStatusEntities
from utils.entities.collection_entities import FeedUrlEntities
from utils.entities.event_entities import EntityTypes
from utils.entities.event_entities import ServiceEntities
from utils.logger import get_logger

logger = get_logger(__name__)


class FeedService(BaseParser):
    """Handles feed-related operations like inserting and checking for duplicates."""

    def __init__(self):
        super().__init__(db_credentials=CREDENTIALS[NEWS_FEEDS["DATABASE"]])
        self.__feed_url_collection_name = (
            APIParameters.FEED_URL_COLLECTION.value
        )
        self.__raw_feed_url_collection_name = (
            APIParameters.RAW_FEED_URL_COLLECTION.value
        )
        self.__feed_parser_job_collection_name = (
            APIParameters.FEED_PARSER_JOB_STATUS_COLLECTION.value
        )

    def get_last_job_state(self, company_cin, parser_service):
        logger.info(
            f"Getting the last job state for the company_cin={company_cin}, "
            f"parser_service={parser_service}"
        )

        query = {}
        if company_cin:
            query.update({FeedParserJobStatusEntities.CIN.value: company_cin})
        if parser_service:
            query.update(
                {
                    FeedParserJobStatusEntities.PARSER_SERVICE.value: parser_service
                }
            )

        last_job_state = self.get(
            data={
                "query": query,
                "collection_name": self.__feed_parser_job_collection_name,
            }
        )

        return last_job_state

    def upsert_job_state(self, **kwargs):
        company_cin = kwargs.get("company_cin")
        parser_service = kwargs.get("parser_service")
        start_date = kwargs.get("start_date")
        end_date = kwargs.get("end_date")
        run_details = kwargs.get("run_details", None)
        if run_details is None:
            run_details = {}

        logger.info(
            f"Updating the job state for the company_cin={company_cin}, "
            f"parser_service={parser_service}, "
            f"start_date={start_date}, end_date={end_date}"
        )

        result = self.update(
            data={
                "filter_criteria": {
                    APIParameters.COMPANY_CIN_FIELD.value: company_cin,
                    "parser_service": parser_service,
                },
                "update_operation": {
                    "$set": {
                        "last_run_start_date": start_date,
                        "last_run_end_date": end_date,
                        FeedParserJobStatusEntities.LAST_RUN_DETAILS.value: run_details,
                    }
                },
                "collection_name": self.__feed_parser_job_collection_name,
            }
        )
        return result

    def check_duplicate_news(
        self,
        **kwargs,
    ) -> bool:
        entity_id = kwargs.get(ServiceEntities.ENTITY_ID.value)
        news_link = kwargs.get(ServiceEntities.NEWS_LINK.value)
        news_title = kwargs.get(ServiceEntities.NEWS_TITLE.value)
        raw_link = kwargs.get(ServiceEntities.RAW_LINK.value)
        entity_type = kwargs.get(
            ServiceEntities.ENTITY_TYPE.value, EntityTypes.COMPANY.value
        )
        logger.debug(
            f"Check the duplicate news for the entity_id={entity_id}, news_title={news_title}, "
            f"news_link={news_link}, raw_link={raw_link}, entity_type={entity_type}"
        )
        duplicate_flag = False
        or_condition = []

        and_condition_query = [{FeedUrlEntities.ENTITY_ID.value: entity_id}]
        if entity_type == EntityTypes.SECTOR.value:
            and_condition_query = []
            or_condition.append({FeedUrlEntities.NEWS_LINK.value: news_link})
        else:
            if news_link:
                or_condition.append(
                    {FeedUrlEntities.NEWS_LINK.value: news_link}
                )
            if news_title:
                or_condition.append(
                    {FeedUrlEntities.RAW_TITLE.value: news_title}
                )
            # if raw_link:
            #     or_condition.append({FeedUrlEntities.RAW_LINK.value: raw_link})

        if len(or_condition) > 0:
            and_condition_query.append({"$or": or_condition})

        if entity_type == EntityTypes.SECTOR.value and len(or_condition) == 0:
            logger.info(
                f"No news_link={news_link}, news_title={news_title}, raw_link={raw_link} "
                f"for the sector news, so not marking it as duplicate entry "
                f"so that it does not get inserted."
            )
            return True

        existing_record = self.get(
            data={
                "query": {"$and": and_condition_query},
                "collection_name": self.__raw_feed_url_collection_name,
            }
        )

        if existing_record:
            duplicate_flag = True
            logger.info(
                f"We have detected this link={news_link} "
                f"for the entity_id={entity_id} as duplicate entry"
            )

        return duplicate_flag

    def insert_feed_data(self, **kwargs):
        entity_type: str = kwargs.get("entity_type")
        entity_id: str = kwargs.get("entity_id")
        company_name: str = kwargs.get("company_name")
        raw_link: str = kwargs.get("raw_link")
        news_link: str = kwargs.get("news_link")
        image_link: str = kwargs.get("image_link")
        published_date: date = kwargs.get("published_date")
        source: str = kwargs.get("source")
        title: str = kwargs.get("title")
        description: str = kwargs.get("description")
        parser_service: str = kwargs.get("parser_service")
        ignore_publishing: bool = kwargs.get("ignore_publishing")
        categories = kwargs.get("categories")
        tags = kwargs.get("tags")

        logger.debug(
            f"Inserting the entity_id={entity_id}, entity_type={entity_type}, "
            f"company_name={company_name}, raw_link={raw_link}, "
            f"news_link={news_link}, image_link={image_link},"
            f"published_date={published_date}, source={source},"
            f"title={title}, description={description}, "
            f"parser_service={parser_service}, ignore_publishing={ignore_publishing}"
            f", categories={categories}, tags={tags}"
        )
        insert_ack = self.insert(
            data={
                "document": {
                    FeedUrlEntities.ENTITY_TYPE.value: entity_type,
                    FeedUrlEntities.ENTITY_ID.value: entity_id,
                    FeedUrlEntities.NAME.value: company_name,
                    FeedUrlEntities.RAW_LINK.value: raw_link,
                    FeedUrlEntities.NEWS_LINK.value: news_link,
                    FeedUrlEntities.IMAGE_LINK.value: image_link,
                    FeedUrlEntities.PUBLISHED_DATE.value: published_date,
                    FeedUrlEntities.SOURCE.value: source,
                    FeedUrlEntities.RAW_TITLE.value: title,
                    FeedUrlEntities.RAW_DESCRIPTION.value: description,
                    FeedUrlEntities.PARSER_SERVICE.value: parser_service.lower(),
                    FeedUrlEntities.PROCESSED_DATE.value: datetime.now(),
                    FeedUrlEntities.IGNORE_PUBLISHING.value: ignore_publishing,
                    FeedUrlEntities.DENOISING_STATUS.value: None,
                    FeedUrlEntities.NAME_PRESENT_IN_TITLE.value: None,
                    FeedUrlEntities.NAME_COUNT_IN_TEXT.value: None,
                    FeedUrlEntities.IS_NEWS.value: None,
                    FeedUrlEntities.IS_DENOISED.value: None,
                    FeedUrlEntities.DENOISED_LINK.value: None,
                    FeedUrlEntities.CATEGORIES.value: categories,
                    FeedUrlEntities.TAGS.value: tags,
                },
                "collection_name": self.__raw_feed_url_collection_name,
            }
        )

        feed_id = str(insert_ack.inserted_id)
        logger.info(
            f"Successfully added the feed to the collection={self.__feed_url_collection_name} with "
            f"id={feed_id} for the company_name={company_name}"
        )

        return feed_id
