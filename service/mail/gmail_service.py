import email
import imaplib
import time
from email.header import decode_header
from email.utils import parseaddr

from bs4 import BeautifulSoup

from config import GMAIL_NEWS_SIZE
from service.mail.mail_service import MailService
from utils.date_utils import str_to_datetime
from utils.entities.gmail_entities import SearchCriteriaEntities
from utils.entities.gmail_entities import SearchResponseEntities
from utils.logger import get_logger

logger = get_logger(__name__)


class GmailService(MailService):
    """
    Service for fetching and processing emails from Gmail using
    IMAP, supporting search filters, HTML parsing, and logging.
    """

    def __init__(self, username: str, password: str):
        self._username = username
        self._password = password
        self._imap_server = "imap.gmail.com"
        self.mail = self._connect_imap_server()

    def _fetch_with_retries(self, email_id):
        for attempt in range(3):
            try:
                status, email_data = self.mail.fetch(email_id, "(RFC822)")
                logger.info(f"Got the status={status} for fetch")
                return email_data
            except imaplib.IMAP4.abort as e:
                logger.warning(f"Fetch failed on attempt {attempt}: {e}")
                time.sleep(2**attempt)
        raise RuntimeError("Failed to fetch email after retries.")

    def _connect_imap_server(self):
        logger.info(f"Connecting to the imap server={self._imap_server}")

        mail = imaplib.IMAP4_SSL(self._imap_server)
        mail.login(self._username, self._password)
        logger.info("Connection successful")

        return mail

    def select_mailbox(self, mailbox: str):
        logger.info(f"Selecting the mailbox={mailbox}")
        self.mail.select(mailbox=mailbox)

    def _get_search_criteria_text(self, search_criteria: dict):
        logger.info(
            f"Getting search criteria text from the search criteria dict={search_criteria}"
        )
        search_criteria_text = ""

        from_address = search_criteria.get(
            SearchCriteriaEntities.FROM_ADDRESS.value
        )
        to_address = search_criteria.get(
            SearchCriteriaEntities.TO_ADDRESS.value
        )
        from_date = search_criteria.get(SearchCriteriaEntities.FROM_DATE.value)
        before_date = search_criteria.get(
            SearchCriteriaEntities.BEFORE_DATE.value
        )
        subject_text = search_criteria.get(
            SearchCriteriaEntities.SUBJECT_TEXT.value
        )
        body_text = search_criteria.get(SearchCriteriaEntities.BODY_TEXT.value)

        if from_address:
            search_criteria_text += f'FROM "{from_address}" '
        if to_address:
            search_criteria_text += f'TO "{to_address}" '
        if from_date:
            search_criteria_text += f'SINCE "{from_date}" '
        if before_date:
            search_criteria_text += f'BEFORE "{before_date}" '
        if subject_text:
            search_criteria_text += f'SUBJECT "{subject_text}" '
        if body_text:
            search_criteria_text += f'BODY "{body_text}" '

        if not search_criteria_text:
            search_criteria_text = "SEEN"
        else:
            search_criteria_text = "(" + search_criteria_text.strip() + ")"

        logger.info(f"Formed search criteria text={search_criteria_text}")

        return search_criteria_text

    def _fetch_email_data(
        self,
        email_ids: list,
        text_format: bool,
        newsletter_date_format: str,
        search_criteria: dict,
    ):
        email_data_list = []
        email_number = search_criteria.get(
            SearchCriteriaEntities.EMAIL_NUMBER.value
        )

        email_ids = email_ids[0].split()
        logger.info(f"Total email_ids retrieved={email_ids}")

        for email_id in email_ids:
            mail_id_decoded = email_id.decode("utf-8")
            if email_number and int(mail_id_decoded) <= email_number:
                logger.info(
                    f"Already we have processed this email_id={mail_id_decoded}, "
                    f"so skipping that email data"
                )
                continue
            email_data = self._fetch_with_retries(email_id)
            raw_email = email_data[0][1]

            msg = email.message_from_bytes(raw_email)

            # Extract details of the mail
            from_address = str(decode_header(msg["From"])[0][0])
            to_address = decode_header(msg["TO"])[0][0]
            subject = decode_header(msg["Subject"])[0][0]
            date = str_to_datetime(
                decode_header(msg["Date"])[0][0], newsletter_date_format
            )
            content_type = decode_header(msg["Content-Type"])[0][0]

            if isinstance(subject, bytes):
                subject = subject.decode("utf-8")

            if from_address:
                print(from_address)
                _, from_address = parseaddr(from_address)

            email_info = {
                SearchResponseEntities.EMAIL_ID.value: mail_id_decoded,
                SearchResponseEntities.FROM_ADDRESS.value: from_address,
                SearchResponseEntities.TO_ADDRESS.value: to_address,
                SearchResponseEntities.SUBJECT.value: subject,
                SearchResponseEntities.DATE.value: date,
                SearchResponseEntities.CONTENT_TYPE.value: content_type,
            }
            html_content = ""

            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    charset = part.get_content_charset() or "utf-8"
                    if content_type == "text/html":
                        html_content = part.get_payload(decode=True).decode(
                            charset, errors="ignore"
                        )
                        break
            else:
                html_content = msg.get_payload(decode=True).decode()

            if text_format:
                soup = BeautifulSoup(html_content, "html.parser")
                body = soup.get_text()
            else:
                body = html_content

            email_info[SearchResponseEntities.BODY.value] = str(body)
            email_data_list.append(email_info)

            if len(email_data_list) >= GMAIL_NEWS_SIZE:
                logger.info(
                    f"Reached max batch size={GMAIL_NEWS_SIZE}, so stopping the remaining emails."
                    f" Last processed email number={mail_id_decoded}"
                )
                break

        return email_data_list

    def get_emails(
        self,
        search_criteria: dict,
        newsletter_date_format: str,
        text_format: bool = False,
    ):
        logger.info(
            f"Getting the emails based on the search criteria={search_criteria} with "
            f"newsletter_date_format={newsletter_date_format}"
        )
        search_criteria_text = self._get_search_criteria_text(
            search_criteria=search_criteria
        )

        status, email_ids = self.mail.search(None, search_criteria_text)

        logger.info(f"Got the status={status} for search")

        return self._fetch_email_data(
            email_ids=email_ids,
            text_format=text_format,
            newsletter_date_format=newsletter_date_format,
            search_criteria=search_criteria,
        )
