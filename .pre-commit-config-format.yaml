 repos:
   - repo: https://github.com/pre-commit/pre-commit-hooks
     rev: v4.4.0
     hooks:
       - id: check-merge-conflict
       - id: check-ast
       - id: check-docstring-first
       - id: trailing-whitespace
       - id: no-commit-to-branch
         args: [ --branch, main, --branch, development, --branch, qa ]
       - id: detect-private-key
       - id: detect-aws-credentials
         args: [ "--allow-missing-credentials" ]
       - id: check-case-conflict
       #     -   id: check-yaml
       - id: check-json
       - id: check-added-large-files
         args: [ '--maxkb=5120' ]
   - repo: https://github.com/asottile/reorder_python_imports
     rev: v3.12.0
     hooks:
       - id: reorder-python-imports
   # - repo: https://github.com/PyCQA/flake8
   #   rev: 6.1.0
   #   hooks:
   #     - id: flake8
   - repo: https://github.com/PyCQA/autoflake
     rev: v2.2.1
     hooks:
       - id: autoflake
         name: autoflake
         entry: autoflake
         language: python
         "types": [ python ]
         require_serial: true
         args:
           - "--in-place"
           - "--expand-star-imports"
           - "--remove-duplicate-keys"
           - "--remove-unused-variables"
           - "--remove-all-unused-imports"
   - repo: https://github.com/psf/black
     rev: 24.2.0
     hooks:
       - id: black
         args:
           - --line-length=79
           - --preview