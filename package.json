{"name": "rss-feed-parser", "version": "1.0.0", "description": "Feed parsers for news feed and ds rss feed", "main": "index.js", "scripts": {"start": "sls offline"}, "devDependencies": {"serverless-offline": "^12.0.4", "serverless-plugin-aws-alerts": "^1.7.5", "serverless-plugin-log-subscription": "^2.2.0", "serverless-prune-plugin": "^2.0.2", "serverless-latest-layer-version": "^2.1.2", "serverless-plugin-utils": "0.2.0"}, "repository": {"type": "git", "url": "git+https://github.com/BlokTrek/rss-feed-parser.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/BlokTrek/rss-feed-parser/issues"}, "homepage": "https://github.com/BlokTrek/rss-feed-parser/tree/development#readme"}