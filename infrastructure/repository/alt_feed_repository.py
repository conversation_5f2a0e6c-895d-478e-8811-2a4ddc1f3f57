from datetime import datetime
from datetime import timedelta
from datetime import timezone

import config
from infrastructure.repository.base_repository import BaseRepository
from utils.exceptions.exceptions import CustomException
from utils.logger import get_logger

logger = get_logger(__name__)


class ALTFeedRepository(BaseRepository):
    """Repository class for managing ALT feed URLs in MongoDB,
    handling active URLs, hashes, and last run timestamps."""

    def __init__(self):
        super().__init__(config.DEFAULT_TIMEOUT_MS)
        self._collection_name = "altFeedURLs"
        logger.info("ALTFeedRepository initialized with MongoDB connection.")

    def get_active_urls(self):
        """Fetch and filter active URLs based on state table metadata."""
        try:
            current_time = datetime.now(tz=timezone.utc)
            urls = self._fetch_all_urls()

            active_urls = []
            for url_data in urls:
                if self._is_recursive(url_data):
                    active_urls.append(url_data)
                elif self._is_due_for_run(url_data, current_time):
                    active_urls.append(url_data)

            urls = "\n".join([url_dict["url"] for url_dict in active_urls])
            logger.info(
                f"Fetched {len(active_urls)} active URLs based on state metadata:\n{urls}"
            )
            return active_urls
        except CustomException as error:
            logger.error(f"Error fetching active URLs from MongoDB: {error}")
            return []

    def _fetch_all_urls(self):
        """Retrieve all URLs and their metadata from the database."""
        query = {
            "$or": [
                {"recursive": True},
                {"recursive": False, "last_run": {"$exists": False}},
                {
                    "recursive": False,
                    "last_run": {"$lt": datetime.now(tz=timezone.utc)},
                },
                {"recursive": False, "last_run": None},
            ]
        }
        projection = {
            "_id": 0,
            "url": 1,
            "recursive": 1,
            "interval": 1,
            "last_run": 1,
        }
        urls = self.find_all(
            collection_name=self._collection_name,
            query=query,
            projection=projection,
        )
        logger.info(f"Retrieved {len(urls)} URLs from the database.")
        return urls

    def _is_recursive(self, url_data):
        """Check if a URL is recursive."""
        return url_data.get("recursive", False)

    def _is_due_for_run(self, url_data, current_time):
        """Check if a non-recursive URL is due for another
        run based on interval and last run time."""
        last_run = url_data.get("last_run")
        interval = url_data.get("interval", 0)

        if last_run:
            next_run_time = last_run + timedelta(minutes=interval)
            if current_time >= next_run_time:
                logger.info(f"URL {url_data['url']} is due for another run.")
                return True
        else:
            logger.info(
                f"URL {url_data['url']} has no last run time, marking as active."
            )
            return True

        return False

    def get_previous_hash(self, url):
        """Fetch the previous hash for a specific URL."""
        try:
            query = {"url": url}
            projection = {"_id": 0, "hash": 1}
            result = self.find_one(
                query=query,
                collection_name=self._collection_name,
                projection=projection,
            )
            previous_hash = result.get("hash") if result else None
            logger.info(f"Previous hash for URL {url}: {previous_hash}")
            return previous_hash
        except CustomException as error:
            logger.error(f"Error retrieving hash for URL {url}: {error}")
            return None

    def update_url_hash(self, url, new_hash):
        """Update the hash for a specific URL."""
        try:
            query = {"url": url}
            update = {"$set": {"hash": new_hash}}
            result = self.update(
                collection_name=self._collection_name,
                query=query,
                update=update,
            )
            logger.info(
                f"Updated hash for URL {url}. Modified count: {result.modified_count}"
            )
            return result.modified_count
        except CustomException as error:
            logger.error(f"Error updating hash for URL {url}: {error}")
            return 0

    def update_last_run(self, url):
        """Update the last_run field for a specific URL to the current time."""
        try:
            current_time = datetime.now(tz=timezone.utc)
            query = {"url": url}
            update = {"$set": {"last_run": current_time}}
            result = self.update(
                collection_name=self._collection_name,
                query=query,
                update=update,
            )
            logger.info(
                f"Updated last run time for URL {url}. Modified count: {result.modified_count}"
            )
            return result.modified_count
        except CustomException as error:
            logger.error(
                f"Error updating last run time for URL {url}: {error}"
            )
            return 0
